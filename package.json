{"name": "aliviae-backend", "version": "1.0.0", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "start": "nest start", "dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "NODE_ENV=production node -r newrelic dist/main", "repl": "nest start --watch --entryFile repl", "format": "prettier --write", "lint": "eslint --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "prepare": "husky"}, "dependencies": {"@aws-sdk/client-s3": "^3.682.0", "@aws-sdk/s3-request-presigner": "^3.682.0", "@nestjs/axios": "^3.0.3", "@nestjs/common": "10.4.4", "@nestjs/config": "^3.2.3", "@nestjs/core": "10.4.4", "@nestjs/jwt": "^10.2.0", "@nestjs/passport": "^10.0.3", "@nestjs/platform-express": "^10.4.6", "@nestjs/swagger": "^7.4.2", "@nestjs/terminus": "^10.2.3", "@prisma/client": "^6.2.1", "axios": "^1.7.7", "bcrypt": "^5.1.1", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "class-validator-cpf": "^1.1.1", "compression": "^1.7.4", "express": "^4.21.1", "firebase-admin": "^13.4.0", "handlebars": "^4.7.8", "helmet": "^8.0.0", "jose": "^5.9.3", "newrelic": "^12.5.1", "nodemailer": "6.9.15", "nodemailer-express-handlebars": "^6.1.2", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "reflect-metadata": "0.2.2", "rxjs": "^7.8.1", "uuid": "^11.0.2"}, "devDependencies": {"@eslint/js": "^9.13.0", "@faker-js/faker": "^9.4.0", "@jest/globals": "^29.7.0", "@nestjs/cli": "10.4.5", "@nestjs/schematics": "^10.1.4", "@nestjs/testing": "^10.4.4", "@swc/cli": "^0.4.1-nightly.20240914", "@swc/core": "^1.10.7", "@swc/helpers": "^0.5.15", "@types/bcrypt": "^5.0.2", "@types/compression": "^1.7.5", "@types/eslint__js": "^8.42.3", "@types/express": "^5.0.0", "@types/jest": "^29.5.14", "@types/jsonwebtoken": "9.0.7", "@types/multer": "^1.4.12", "@types/newrelic": "^9.14.5", "@types/node": "^20.3.1", "@types/nodemailer": "^6.4.16", "@types/nodemailer-express-handlebars": "^4.0.5", "@types/passport-jwt": "^4.0.1", "@types/supertest": "6.0.2", "@typescript-eslint/eslint-plugin": "8.7.0", "@typescript-eslint/parser": "8.7.0", "eslint": "^9.13.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-jest": "^28.8.3", "eslint-plugin-prettier": "^5.2.1", "globals": "^15.11.0", "husky": "^9.1.6", "jest": "29.7.0", "lint-staged": "^15.2.10", "prettier": "^3.3.3", "prisma": "^6.2.1", "source-map-support": "^0.5.21", "supertest": "7.0.0", "ts-jest": "^29.2.5", "ts-loader": "9.5.1", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.6.3", "typescript-eslint": "^8.11.0", "webpack": "5.94", "webpack-cli": "5.1.4"}, "prisma": {"seed": "ts-node --swc prisma/seeds.ts"}, "lint-staged": {"*.ts": ["prettier --write", "eslint --fix", "jest --passWithNoTests"]}}