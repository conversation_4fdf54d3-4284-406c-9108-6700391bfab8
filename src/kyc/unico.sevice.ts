import { stringify } from 'qs';

import { BadRequestException, Injectable, Logger } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@nestjs/config';
import { JwtService } from '@nestjs/jwt';

import { catchError, lastValueFrom, map } from 'rxjs';
import PrismaService from '@/utils/prisma/prisma.service';

type CreateMessageDto = { Id: string };

type CreateMessageRequest = {
  subject: {
    Code: string;
    Name: string;
    Gender?: string;
    BirthDate?: string;
    Email?: string;
    Phone?: string;
  };
  indexes: {
    key: string;
    value: string;
    page: string;
  }[];
  template: string;
  send: boolean;
  enableQRCode: boolean;
  to?: string;
};

type ProcessStatus = {
  Documents: Document[];
  HasBiometry: boolean;
  Id: string;
  Liveness: number;
  Status: number;
  Score: number;
  OCRCode: number;
  FaceMatch: number;
  CurrentStep: number;
};

@Injectable()
class UnicoService {
  readonly #logger = new Logger(UnicoService.name);
  readonly #url = '/services/v3/AcessoService.svc/messages';

  constructor(
    private readonly http: HttpService,
    private readonly prisma: PrismaService,
    private readonly config: ConfigService,
    private readonly jwt: JwtService,
  ) {}

  /**
   * Realiza uma requisição o para obter o token de acesso para
   * realizar operações na API da Unico
   *
   * @returns Um objeto com o token de acesso.
   */
  async getAuthorizationToken() {
    const baseURL = this.config.getOrThrow('kyc.unico.id.url');
    const privateKey = this.config.getOrThrow('kyc.unico.id.private-key');
    const tenant = this.config.getOrThrow('kyc.unico.id.tenant');

    const payload = { scope: '*' };
    const assertion = await this.jwt.signAsync(payload, {
      algorithm: 'RS256',
      expiresIn: '1h',
      issuer: `aliviae@${tenant}.iam.acesso.io`,
      audience: baseURL,
      secret: privateKey,
    });
    const data = {
      assertion,
      grant_type: 'urn:ietf:params:oauth:grant-type:jwt-bearer',
    };
    const headers = { 'Content-Type': 'application/x-www-form-urlencoded' };

    try {
      const result = await lastValueFrom(
        this.http
          .post('/oauth2/token', stringify(data), { headers, baseURL })
          .pipe(map((response) => response.data)),
      );

      return { access_token: result.access_token as string };
    } catch (error) {
      console.error(error.response.data);
      throw new Error(error);
    }
  }

  /**
   * Cria um processo antifraude na único caso nao exista
   *
   * @param id id do beneficiário para pegar dados do usuário no banco para montar requisição
   * @returns retorna id do processo da único que será salvo no banco como process_id.
   */
  async createMessageProcess(id: number): Promise<CreateMessageDto> {
    const user = await this.prisma.users.findUnique({ where: { id } });
    if (!user) throw new BadRequestException('Unable to find user');

    //formata data de nascimento do usuário
    const day = String(user.birthDate!.getUTCDate()).padStart(2, '0');
    const month = String(user.birthDate!.getUTCMonth()! + 1).padStart(2, '0');
    const year = user.birthDate!.getUTCFullYear();
    const formattedDate = `${year}-${month}-${day}`;

    if (!user.name) throw new BadRequestException('Usuário sem nome');
    if (!user.phone)
      throw new BadRequestException('Usuário sem telefone cadastrado');
    if (user.phone) user.phone = '55' + user.phone;

    const data: CreateMessageRequest = {
      subject: {
        Code: user.cpf,
        Name: user.name,
        Gender: user?.gender![0].toUpperCase(),
        BirthDate: formattedDate,
        Email: user.email || undefined,
        Phone: user.phone,
      },
      indexes: [
        {
          key: 'aliviae',
          value: '0',
          page: 'SMS',
        },
      ],
      template: 'ALIVIAE',
      send: true,
      enableQRCode: false,
      to: user.phone,
    };

    const credentials = await this.getAuthorizationToken();
    const headers = {
      ...this.http.axiosRef.defaults.headers.common,
      Authorization: `Bearer ${credentials.access_token}`,
    };

    const result = await lastValueFrom(
      this.http.post(this.#url, data, { headers }).pipe(
        map((response) => response.data),
        // verifica se houve algum erro na busca de usuário
        catchError((err) => {
          const msg = `Erro ao identificar o usuário ${user.id} na base Unico`;
          this.#logger.error(msg, err.config);
          throw new BadRequestException(msg);
        }),
      ),
    );
    return result;
  }

  /**
   * Obtém o status de um processo antifraude com base no ID fornecido.
   *
   * @param processId ID do processo antifraude a ser consultado.
   * @returns Retorna um objeto contendo o status do processo, incluindo documentos associados, biometria, pontuação e outros detalhes.
   */
  async getProcessStatus(processId: string): Promise<ProcessStatus> {
    const credentials = await this.getAuthorizationToken();
    const headers = {
      ...this.http.axiosRef.defaults.headers.common,
      Authorization: `Bearer ${credentials.access_token}`,
    };
    // pega processo antifraude
    const url = this.#url + '/' + processId;
    const result = await lastValueFrom(
      this.http.get(url, { headers }).pipe(
        map(({ data }) => data),
        catchError((err) => {
          const msg = `Erro ao identificar processo anti-fraude ${processId}`;
          this.#logger.error(msg, err.config);
          throw new BadRequestException(msg);
        }),
      ),
    );
    return result;
  }
}
export default UnicoService;
