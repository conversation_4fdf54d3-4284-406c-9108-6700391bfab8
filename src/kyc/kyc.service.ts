import { BadRequestException, Injectable } from '@nestjs/common';

import UnicoService from './unico.sevice';

import PrismaService from '@/utils/prisma/prisma.service';

type AntiFraud = { processId: string };

@Injectable()
class KycService {
  // readonly #logger = new Logger(KycService.name);

  constructor(
    private readonly prisma: PrismaService,
    private readonly unico: UnicoService,
  ) {}

  /**
   * Enviar informações do usuário para único iniciar o processo de mensagem (antifraude)
   *
   * @param id id do usuário utilizado para pegar seus dados no banco para montar requisição para único
   * @returns retorna processId e url do QRCode  para que o usuário envie foto dos documentos para único
   */
  async createDocumentProcess(id: number): Promise<AntiFraud> {
    const user = await this.prisma.users.findUnique({
      where: { id },

      select: {
        antifraudProccess: {
          select: {
            processId: true,
          },
        },
      },
    });
    if (!user) throw new BadRequestException('Usuário não encontrado');
    //! se usuário puder cadastrar um novo documento e subsituir, remover esta validação
    if (user.antifraudProccess?.processId)
      throw new BadRequestException('Usuário já cadastrou seu documento');
    // cria o processo antifraude
    const { Id: processId } = await this.unico.createMessageProcess(id);
    //TODO: fazer retornar a url para fazer o processo de envio de documentos

    //TODO: vincular id do processo antifraude a tabela antifraudProccess
    // await this.prisma.users.update({ where: { id }, data: { processId } });

    return { processId };
  }

  /**
   * Deverá verificar se há documento para salvar no banco
   *
   * @param id id do usuário utilizado para pegar seus dados no banco e consultar na único
   * @returns Objeto contendo o status do processo KYC:
   */
  async getAntifraudStatus(id: number): Promise<void> {
    //TODO: fluxo será redefinido, consultar imagem para salvar no banco
    const user = await this.prisma.users.findUnique({
      where: { id },
      select: {
        antifraudProccess: {
          select: {
            processId: true,
          },
        },
      },
    });
    if (!user) throw new BadRequestException('Usuário não encontrado');
    if (!user?.antifraudProccess?.processId) {
      throw new BadRequestException('Usuário não iniciou processo anti-fraude');
    }
    // Consulta status do processo antifraude na único
    const result = await this.unico.getProcessStatus(
      user?.antifraudProccess?.processId,
    );
    console.log(result);
    //TODO: pegar o process_id e salvar na tabela fileProcess
    //TODO: pegar imagens para enviar para S3
  }
}

export default KycService;
