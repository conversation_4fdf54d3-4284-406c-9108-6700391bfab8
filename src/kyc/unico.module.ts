import { Modu<PERSON> } from '@nestjs/common';
import { HttpModule } from '@nestjs/axios';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { JwtService } from '@nestjs/jwt';

import UnicoService from './unico.sevice';
import KycService from './kyc.service';

const UnicoHttpModule = HttpModule.registerAsync({
  imports: [ConfigModule],
  inject: [ConfigService],
  useFactory: async (config: ConfigService) => ({
    baseURL: config.getOrThrow('kyc.unico.message.url'),
    headers: {
      Accept: 'application/json',
      'Content-Type': 'application/json',
      APIKEY: config.getOrThrow('kyc.unico.message.token'),
    },
  }),
});

@Module({
  imports: [UnicoHttpModule],
  providers: [UnicoService, KycService, ConfigService, JwtService],
  exports: [UnicoService, KycService, UnicoHttpModule, JwtService],
})
export default class UnicoModule {}
