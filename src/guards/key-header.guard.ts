import {
  Injectable,
  CanActivate,
  ExecutionContext,
  ForbiddenException,
} from '@nestjs/common';

@Injectable()
export class KeyHeaderGuard implements CanActivate {
  canActivate(context: ExecutionContext): boolean {
    const request = context.switchToHttp().getRequest();
    const keyHeader = request.headers['x-validator'];
    if (keyHeader != process.env.POPULATE_KEY) {
      throw new ForbiddenException('Incorrect or Missing validator header');
    }
    return true;
  }
}
