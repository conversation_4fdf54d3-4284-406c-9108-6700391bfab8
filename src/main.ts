import { ValidationPipe, VersioningType } from '@nestjs/common';
import { NestFactory } from '@nestjs/core';
import { NestExpressApplication } from '@nestjs/platform-express';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import { ConfigService } from '@nestjs/config';

import helmet from 'helmet';
import compression from 'compression';

import HttpExceptionFilter from './utils/http-exception.filter';
import NewRelicLogger from './utils/new-relic/new-relic.logger';

import AppModule from './app.module';
import { Request, Response, NextFunction } from 'express';

(async () => {
  const app = await NestFactory.create<NestExpressApplication>(AppModule);
  const config = app.get(ConfigService);

  app.enableCors();

  // Permite que Nest leia o IP real do proxy
  app.set('trust proxy', true);

  app.enableVersioning({ type: VersioningType.URI, prefix: 'api/v' });

  // aplica o Helmet com configuração personalizada
  app.use(
    helmet({
      contentSecurityPolicy: {
        directives: {
          defaultSrc: ["'self'"],
          frameAncestors: ["'none'"],
        },
      },
      frameguard: {
        action: 'deny',
      },
      noSniff: true,
      strictTransportSecurity: {
        maxAge: 15768000, // 6 meses
        includeSubDomains: true,
        preload: false,
      },
      referrerPolicy: {
        policy: 'strict-origin-when-cross-origin',
      },
    }),
  );

  // Header adicional de segurança: Permissions-Policy
  app.use((req: Request, res: Response, next: NextFunction) => {
    res.setHeader('Permissions-Policy', 'camera=(), microphone=()');
    next();
  });

  // aplica compressão nas responses da aplicação
  app.use(compression());

  // aplica a validação do class-validator em toda a aplicação
  app.useGlobalPipes(new ValidationPipe());

  // aplica o filtro de exceções HTTP globalmente
  const newRelicLogger = app.get(NewRelicLogger);
  app.useGlobalFilters(new HttpExceptionFilter(newRelicLogger));

  const swaggerConfig = new DocumentBuilder()
    .setTitle('Aliviaê')
    .setDescription('API de integração do Aliviaê')
    .setVersion('1.0')
    .addBearerAuth()
    .build();

  const document = SwaggerModule.createDocument(app, swaggerConfig);
  SwaggerModule.setup('api', app, document);

  await app.listen(config.get('app.port', 3000), '0.0.0.0');
})();
