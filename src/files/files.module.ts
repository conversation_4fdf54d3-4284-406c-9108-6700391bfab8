import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import FilesService from './files.service';
import S3Service from '@/utils/aws/s3.service';
import { JwtModule } from '@nestjs/jwt';

const JWTModule = JwtModule.registerAsync({
  imports: [ConfigModule],
  inject: [ConfigService],
  useFactory: async (config: ConfigService) => ({
    signOptions: { expiresIn: config.get<number>('app.jwt.expiration')! * 60 },
    secret: config.get<string>('app.jwt.secret'),
  }),
});

@Module({
  imports: [ConfigModule, JWTModule],
  providers: [FilesService, S3Service],
  exports: [FilesService],
})
export class FilesModule {}
