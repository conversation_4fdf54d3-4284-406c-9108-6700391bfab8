import { Injectable } from '@nestjs/common';
import S3Service from '@/utils/aws/s3.service';
import { lastValueFrom } from 'rxjs';

@Injectable()
class FilesService {
  constructor(private readonly s3: S3Service) {}

  /**
   * Envia o documento para upload na AWS S3
   *
   * @param file Arquivo enviado para o serviço de upload
   * @returns Informações do arquivo enviado
   */

  async uploadAntiFraud(file: Express.Multer.File): Promise<any> {
    const uploadedFile = lastValueFrom(this.s3.upload(file, 'anti-fraude'));
    return uploadedFile;
  }
}

export default FilesService;
