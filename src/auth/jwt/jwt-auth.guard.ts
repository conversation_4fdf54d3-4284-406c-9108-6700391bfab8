import {
  ExecutionContext,
  Injectable,
  Logger,
  UnauthorizedException,
} from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { AuthGuard } from '@nestjs/passport';

import { type Request } from 'express';

/**
 * O objetivo é usarmos este Guard como base nas implementações de cada módulo, caso
 * existam especificidades a serem implementadas e a autenticação ainda seja via JWT.
 *
 * @see https://docs.nestjs.com/recipes/passport#extending-guards
 */
@Injectable()
export default class JwtAuthGuard extends AuthGuard('jwt') {
  readonly #logger = new Logger(JwtAuthGuard.name);

  constructor(protected readonly jwt: JwtService) {
    super();
  }

  /**
   * Extrai o token de autentização da requisição enviada.
   */
  protected extractTokenFromHeader(request: Request): string | undefined {
    const [type, token] = request.headers.authorization?.split(' ') ?? [];

    return type === 'Bearer' ? token : undefined;
  }

  /**
   * Verifica se o token de autenticação está correto e persiste entre as requisições.
   */
  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest<Request>();

    const token = this.extractTokenFromHeader(request);
    if (!token) {
      throw new UnauthorizedException(
        'Você não possui o token de autenticação!',
      );
    }

    this.#logger.debug('Verificando token de autenticação...');

    try {
      request.user = await this.jwt.verifyAsync<Express.User>(token);
    } catch (error) {
      this.#logger.error(error.message);
      throw new UnauthorizedException(error.message);
    }

    return true;
  }
}
