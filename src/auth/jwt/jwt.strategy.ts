import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { PassportStrategy } from '@nestjs/passport';
import { JwtService } from '@nestjs/jwt';

import { ExtractJwt, Strategy } from 'passport-jwt';

@Injectable()
export default class JwtStrategy extends PassportStrategy(Strategy) {
  constructor(
    private readonly config: ConfigService,
    private readonly jwt: JwtService,
  ) {
    const ignoreExpiration = config.get('app.env') === 'development';
    const secretOrKey = config.get<string>('app.jwt.secret');

    super({
      secretOrKey,
      ignoreExpiration,
      jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
    });
  }

  async validate(payload: Express.User) {
    return payload;
  }
}
