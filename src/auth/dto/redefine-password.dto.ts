import { ApiProperty } from '@nestjs/swagger';

import {
  IsNotEmpty,
  IsNumberString,
  IsStrongPassword,
  Length,
} from 'class-validator';

class RedefinePasswordDto {
  @IsNumberString()
  @Length(6)
  @IsNotEmpty()
  @ApiProperty({ description: 'Código PIN para verificação multi fator' })
  pinCode: string;

  @IsStrongPassword()
  @IsNotEmpty()
  @ApiProperty({ description: 'Nova senha que será utilizada' })
  newPassword: string;
}

export default RedefinePasswordDto;
