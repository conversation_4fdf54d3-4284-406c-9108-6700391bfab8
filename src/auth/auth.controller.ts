import {
  BadRequestException,
  Body,
  Controller,
  Post,
  Request,
  UnauthorizedException,
  UseGuards,
} from '@nestjs/common';
import { ApiBody, ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';

import { catchError, from, map, of, switchMap } from 'rxjs';

import UserService from '@/user/user.service';

import AuthGuard from './auth.guard';
import AuthService from './auth.service';

import LoginDto from './dto/login.dto';
import ForgotPasswordDto from './dto/forgot-password.dto';
import RedefinePasswordDto from './dto/redefine-password.dto';

@ApiTags('auth')
@UseGuards(AuthGuard)
@Controller({ path: 'auth', version: '1' })
export default class AuthController {
  constructor(
    private readonly auth: AuthService,
    private readonly user: UserService,
  ) {}

  @ApiOperation({ summary: 'Realiza o login do usuário' })
  @ApiBody({ type: LoginDto, description: 'Credenciais de acesso do usuário' })
  @ApiResponse({ status: 201, description: 'Login realizado com sucesso' })
  @ApiResponse({ status: 401, description: 'E-mail ou senha incorretos' })
  @Post('/login')
  async login(@Body() data: LoginDto) {
    const { password, cpf } = data;

    // verifica a senha está correta
    const authenticated = await this.auth.validateCredentials(cpf, password);
    if (!authenticated) {
      throw new UnauthorizedException('E-mail ou senha incorretos');
    }

    // busca demais informações do usuário
    const user = await this.user.findByCpf(cpf);
    if (!user) throw new UnauthorizedException('E-mail ou senha incorretos');

    //TODO: implementar verificação se usuário enviou documentos

    // monta os dados contidos no payload do JWT
    const payload = { id: user.id, cpf: cpf, email: user.email! };
    const authToken = await this.auth.generateToken(payload);

    return { id: user.id, ...authToken };
  }

  @ApiOperation({ summary: 'Realiza a atualização do JWT do usuário' })
  @Post('/refresh-token')
  async refreshToken(@Request() req: Express.Request) {
    const { id } = req.user!;

    // se o usuário for do fornecedor topmed, não precisa buscar no banco
    if (req.user?.supplier === 'topmed') {
      const authToken = await this.auth.generateToken({
        id: 0,
        cpf: '',
        email: '',
        supplier: 'topmed',
      });
      return { id: 0, ...authToken };
    }

    const user = await this.user.findById(id);
    if (!user) throw new UnauthorizedException('Usuário não existe');

    // monta os dados contidos no payload do JWT
    const payload = { id: user.id, cpf: user.cpf, email: user.email! };
    const authToken = await this.auth.generateToken(payload);

    return { id: user.id, ...authToken };
  }

  @ApiOperation({
    summary: 'Inicia o processo de redefinição de senha',
    description: 'Envia um e-mail com um código para realizar a redefinição',
  })
  @ApiResponse({ status: 201, description: 'E-mail enviado com sucesso' })
  @ApiResponse({
    status: 400,
    description: 'Usuário não existe ou o e-mail é inválido',
  })
  @ApiBody({
    type: ForgotPasswordDto,
    description: 'Informações do usuário para recuperação da senha',
  })
  @Post('/forgot-password')
  forgotPassword(@Body() data: ForgotPasswordDto) {
    const { cpf } = data;

    return from(this.user.forgotPassword(cpf)).pipe(
      catchError((err) => {
        throw new BadRequestException(err.message);
      }),
    );
  }

  @Post('/verify-pin-code')
  verifyPinCode(@Body() pinCodeData: { pinCode: string }) {
    const { pinCode } = pinCodeData;

    return from(this.user.verifyPinCode(pinCode)).pipe(
      // dispara uma exceção se o PIN não estiver vinculado à nenhum usuário
      catchError(() => {
        throw new BadRequestException('Código PIN incorreto');
      }),
      // verifica se o PIN é válido
      switchMap((isValid) => {
        if (!isValid) {
          throw new BadRequestException('Código PIN incorreto');
        }

        return of(true);
      }),
    );
  }

  @ApiOperation({ summary: 'Redefine a senha do usuário' })
  @ApiResponse({ status: 201, description: 'Senha redefinida com sucesso' })
  @ApiResponse({
    status: 401,
    description: 'O código PIN é inválido ou o usuário não existe',
  })
  @ApiBody({
    type: RedefinePasswordDto,
    description: 'Informações necessárias para redefinição da senha',
  })
  @Post('/redefine-password')
  redefinePassword(@Body() data: RedefinePasswordDto) {
    const { pinCode, newPassword } = data;

    return from(this.user.verifyPinCode(pinCode)).pipe(
      // verifica se houve algum erro ao validar o PIN
      catchError(() => {
        throw new BadRequestException('Código PIN incorreto');
      }),
      // redefine a senha do usuário após MFA
      switchMap(() => from(this.user.redefinePassword(pinCode, newPassword))),
      // verifica se a redefinição aconteceu com sucesso
      catchError(() => {
        throw new BadRequestException('Houve um problema ao redefinir a senha');
      }),
      map(() => ({ message: 'Senha redefinida com sucesso!' })),
    );
  }
}
