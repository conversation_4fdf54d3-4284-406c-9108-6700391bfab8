import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { JwtService } from '@nestjs/jwt';

import UserService from '@/user/user.service';
import EncryptService from '@/utils/encrypt/encrypt.service';

@Injectable()
class AuthService {
  readonly #logger = new Logger(AuthService.name);

  constructor(
    private readonly config: ConfigService,
    private readonly encrypt: EncryptService,
    private readonly user: UserService,
    private readonly jwt: JwtService,
  ) {}

  /**
   * Verifica se a senha informada como parâmetro está correta.
   *
   * @param cpf CPF de identificação do usuário na base.
   * @param password Senha que será verificada.
   * @returns A confirmação da validade da senha.
   */
  async validateCredentials(cpf: string, password: string) {
    const user = await this.user.findUnique({ where: { cpf } });
    if (!user) return false;

    return this.encrypt.comparePasswords(password, user.password!);
  }

  /**
   * Generates a JWT token that expires in 30 minutes.
   *
   * The token is generated using the JWT_SECRET environment variable, which
   * must be set before starting the application. If the variable is not set, an
   * error is thrown.
   *
   * @returns An object with the generated token and its expiration date as an ISO string.
   * @throws {Error} If JWT_SECRET environment variable is not defined or if an
   * error occurs while generating the token.
   */
  async generateToken(payload: Express.User) {
    const secret = this.config.getOrThrow<string>('app.jwt.secret');
    const expiration = this.config.getOrThrow<number>('app.jwt.expiration');

    try {
      const token = await this.jwt.signAsync(payload, {
        secret,
        expiresIn: expiration * 60,
      });

      const expiresIn = new Date();
      expiresIn.setMinutes(expiresIn.getMinutes() + expiration);

      return { token, expiresIn: expiresIn.toISOString() };
    } catch (error) {
      this.#logger.error('Error generating token:', error);
      throw new Error('Failed to generate token');
    }
  }
}

export default AuthService;
