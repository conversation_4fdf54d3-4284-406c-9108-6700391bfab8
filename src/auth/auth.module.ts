import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';

import { JwtModule } from '@nestjs/jwt';

import UserModule from '@/user/user.module';
import UserService from '@/user/user.service';
import EncryptModule from '@/utils/encrypt/encrypt.module';

import AuthService from './auth.service';
import AuthController from './auth.controller';

import JwtStrategy from './jwt/jwt.strategy';
import AuthGuard from '@/auth/auth.guard';

const JWTModule = JwtModule.registerAsync({
  imports: [ConfigModule],
  inject: [ConfigService],
  useFactory: async (config: ConfigService) => ({
    signOptions: {
      expiresIn: config.getOrThrow<number>('app.jwt.expiration') * 60 * 60,
    },
    secret: config.get<string>('app.jwt.secret'),
  }),
});

@Module({
  imports: [JWTModule, UserModule, EncryptModule],
  providers: [AuthService, ConfigService, JwtStrategy, UserService, AuthGuard],
  exports: [AuthService, JWTModule, JwtStrategy],
  controllers: [AuthController],
})
export default class AuthModule {}
