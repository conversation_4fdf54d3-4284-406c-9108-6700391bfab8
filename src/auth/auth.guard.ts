import { ExecutionContext, Injectable } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';

import JwtAuthGuard from './jwt/jwt-auth.guard';
import type AuthController from './auth.controller';

import type { Request } from 'express';

@Injectable()
class AuthGuard extends JwtAuthGuard {
  constructor(protected readonly jwt: JwtService) {
    super(jwt);
  }

  async handleRefreshToken(request: Request) {
    const token = this.extractTokenFromHeader(request);
    if (!token) return false;

    request.user = await this.jwt.verifyAsync<Express.User>(token, {
      ignoreExpiration: true,
    });

    return true;
  }

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const handler = context.getHandler().name;
    const request = context.switchToHttp().getRequest<Request>();

    switch (handler as keyof AuthController) {
      case 'refreshToken':
        return this.handleRefreshToken(request);
      default:
        return true;
    }
  }
}

export default AuthGuard;
