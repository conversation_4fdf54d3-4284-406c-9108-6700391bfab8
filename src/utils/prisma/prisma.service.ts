import {
  Injectable,
  Logger,
  On<PERSON><PERSON>ule<PERSON><PERSON><PERSON>,
  OnModuleInit,
} from '@nestjs/common';

import { PrismaClient } from '@prisma/client';

@Injectable()
class PrismaService
  extends PrismaClient
  implements OnModuleInit, OnModuleDestroy
{
  readonly #logger = new Logger(PrismaService.name);

  constructor() {
    super({ log: ['info', 'warn', 'error'] });

    // FIXME: substituir por $extends
    this.$use(async (params, next) => {
      const start = performance.now();
      const result = await next(params);
      const time = (performance.now() - start).toFixed(3);

      this.#logger.debug(
        `Query '${params?.model}.${params.action}' levou ${time}ms`,
      );

      return result;
    });

    /* SOFT DELETE MIDDLEWARE */
    // https://www.prisma.io/docs/orm/prisma-client/client-extensions/middleware/soft-delete-middleware

    const softDeleteModels = [
      'Users',
      'TelevetUsers',
      'TelemedUsers',
      'UserDependents',
      'UserAddresses',
      'Addresses',
    ];

    // $extends ainda não suporta reescrever a action diretamente.
    this.$use(async (params, next) => {
      if (softDeleteModels.includes(params.model as string)) {
        if (params.action === 'delete') {
          params.action = 'update';
          params.args['data'] = { deletedAt: new Date() };
        }
        if (params.action === 'deleteMany') {
          params.action = 'updateMany';
          params.args['data'] = {
            ...params.args.data,
            deletedAt: new Date(),
          };
        }
      }
      return next(params);
    });

    // Aplica filtro automático de soft delete nas operações selecionadas
    const extendedClient = this.$extends({
      name: 'filterSoftDeleted',
      query: {
        $allModels: {
          async $allOperations({ model, operation, args, query }) {
            const operationsToFilter = [
              'count',
              'findUnique',
              'findFirst',
              'findUniqueOrThrow',
              'findFirstOrThrow',
              'findMany',
              'update',
              'updateMany',
            ];

            if (
              softDeleteModels.includes(model) &&
              operationsToFilter.includes(operation) &&
              'where' in args
            ) {
              // Permite sobrescrever deletedAt se já estiver definido no where.
              args.where = { deletedAt: null, ...args.where };
            }

            return query(args);
          },
        },
      },
    });

    Object.assign(this, extendedClient);
  }

  async onModuleInit() {
    const start = performance.now();
    await this.$connect();
    const time = (performance.now() - start).toFixed(3);

    this.#logger.debug(`Conexão com o banco estabelecida em ${time}ms!`);
  }

  async onModuleDestroy() {
    await this.$disconnect();
    this.#logger.debug('Conexão com o banco de dados encerrada!');
  }
}

export default PrismaService;
