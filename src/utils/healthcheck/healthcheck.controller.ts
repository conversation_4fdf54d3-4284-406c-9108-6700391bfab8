import { Controller, Get } from '@nestjs/common';
import {
  HealthCheck,
  HealthCheckService,
  HttpHealthIndicator,
  PrismaHealthIndicator,
} from '@nestjs/terminus';

import PrismaService from '@/utils/prisma/prisma.service';

@Controller('healthcheck')
class HealthcheckController {
  constructor(
    private health: HealthCheckService,
    private http: HttpHealthIndicator,
    private db: PrismaHealthIndicator,
    private prisma: PrismaService,
    // private memory: MemoryHealthIndicator,
  ) {}

  @Get('/')
  @HealthCheck()
  async check() {
    return this.health.check([
      // verifica se a aplicação possui acesso a internet
      () => this.http.pingCheck('http', 'https://google.com'),
      // verifica se a conexão com o banco de dados está estável
      () => this.db.pingCheck('db', this.prisma),
      // verifica se o consumo de memória atinge 1GB
      // () => this.memory.checkHeap('memory_heap', 1024 * 1024 * 1024),
    ]);
  }
}

export default HealthcheckController;
