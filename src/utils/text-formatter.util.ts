/**
 * Utilitários para formatação de texto.
 * Centraliza funções de capitalização e formatação para reutilização.
 */
export class TextFormatter {
  /**
   * Formata texto com todas as palavras em formato título (primeira letra maiúscula).
   *
   * @param text Texto a ser formatado.
   * @returns Texto com todas as palavras em formato título.
   *
   * @example
   * TextFormatter.toTitleCase('jo<PERSON> da silva') // '<PERSON>'
   * TextFormatter.toTitleCase('MARIA SANTOS') // 'Maria Santos'
   */
  static toTitleCase(text: string): string {
    if (!text) return '';

    return text
      .split(' ')
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
      .join(' ');
  }

  /**
   * Formata texto com apenas a primeira letra maiúscula.
   *
   * @param text Texto a ser formatado.
   * @returns Texto com apenas a primeira letra maiúscula.
   *
   * @example
   * TextFormatter.toSentenceCase('apto 101') // 'Apto 101'
   * TextFormatter.toSentenceCase('CASA AZUL') // 'Casa azul'
   */
  static toSentenceCase(text: string): string {
    if (!text) return '';

    return text.charAt(0).toUpperCase() + text.slice(1).toLowerCase();
  }

  /**
   * Remove caracteres não numéricos de uma string e remove espaços.
   *
   * @param text Texto a ser limpo.
   * @returns Apenas os dígitos, sem espaços.
   *
   * @example
   * TextFormatter.extractNumbers('(11) 99999-9999') // '11999999999'
   * TextFormatter.extractNumbers('123.456.789-01') // '12345678901'
   * TextFormatter.extractNumbers(' 123 456 ') // '123456'
   */
  static extractNumbers(text: string): string {
    if (!text) return '';

    return text.trim().replace(/[^0-9]/g, '');
  }
}
