import {
  CallHandler,
  ExecutionContext,
  Injectable,
  NestInterceptor,
} from '@nestjs/common';

import { from, map, of, switchMap } from 'rxjs';

import EncryptService from './encrypt.service';

const IGNORED_ROUTES = [
  '/api/v1/auth/login',
  '/api/v1/integrations/topmed/login',
  '/healthcheck',
];

@Injectable()
class JweInterceptor implements NestInterceptor {
  constructor(private readonly encrypt: EncryptService) {}

  intercept(ctx: ExecutionContext, next: CallHandler) {
    const url = ctx.switchToHttp().getRequest().url;

    return next.handle().pipe(
      map((response) => {
        // verifica se a requisição deve ser interceptada
        if (IGNORED_ROUTES.includes(url)) return of(response);

        return from(this.encrypt.encryptJWE(response));
      }),
      switchMap((response) => response),
    );
  }
}

export default JweInterceptor;
