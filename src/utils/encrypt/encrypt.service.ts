import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

import * as jose from 'jose';
import * as bcrypt from 'bcrypt';

@Injectable()
class EncryptService {
  readonly #logger = new Logger(EncryptService.name);

  constructor(private readonly config: ConfigService) {}

  /**
   * Returns the JWE secret key as a Uint8Array.
   *
   * The key is read from the JWE_SECRET environment variable, which must be set
   * before starting the application. If the variable is not set, an error is
   * thrown.
   *
   * @returns {Uint8Array} The JWE secret key as a Uint8Array.
   * @throws {Error} If JWE_SECRET environment variable is not defined.
   */
  private getSecretKey(): Uint8Array {
    const secretKey = this.config.getOrThrow<string>('app.jwe.secret');
    return new TextEncoder().encode(secretKey);
  }

  /**
   * Encrypts a payload using the JWE secret key, returning the encrypted payload
   * as a string.
   *
   * The payload is first converted to a JSON string, then encrypted using the
   * JWE secret key. The Compact JWE format is used, with the following
   * parameters:
   *
   * - alg: dir (direct key)
   * - enc: A256GCM (AES-256-GCM)
   *
   * If an error occurs during the encryption process, an error is thrown.
   *
   * @param {any} payload The payload to be encrypted.
   * @returns The encrypted payload as a string.
   * @throws {Error} If an error occurs during the encryption process.
   */
  async encryptJWE(payload: any) {
    if (this.config.get('app.env') == 'local') {
      return Promise.resolve(payload);
    }

    const key = this.getSecretKey();
    try {
      const start = performance.now();
      const jwe = await new jose.CompactEncrypt(
        new TextEncoder().encode(JSON.stringify(payload)),
      )
        .setProtectedHeader({
          alg: 'dir', // Algoritmo de chave direta
          enc: 'A256GCM', // Algoritmo de criptografia
        })
        .encrypt(Buffer.from(key));

      const end = (performance.now() - start).toFixed(2);
      this.#logger.log(`Encriptação JWE: ${end} ms`);

      return { payload: jwe };
    } catch (error) {
      this.#logger.error('Erro ao gerar JWE:', error);
      throw new Error('Failed to generate JWE');
    }
  }

  /**
   * Decrypts a JWE payload using the JWE secret key, returning the decrypted
   * payload as a JSON object.
   *
   * The payload is first decrypted using the JWE secret key. If the decryption
   * process fails, an error is thrown. The decrypted payload is then parsed as
   * JSON and returned.
   *
   * @param {string} payload The JWE payload to be decrypted.
   * @returns {Promise<any>} The decrypted payload as a JSON object.
   * @throws {Error} If an error occurs during the decryption process.
   */
  async decryptJWE(payload: string): Promise<any> {
    const key = this.getSecretKey();

    try {
      const { plaintext, protectedHeader } = await jose.compactDecrypt(
        payload,
        key,
      );
      console.log(protectedHeader);
      return JSON.parse(new TextDecoder().decode(plaintext));
    } catch (error) {
      console.error('Erro ao descriptografar JWE:', error);
      throw new Error('Failed to decrypt JWE');
    }
  }

  /**
   * Hash a plain text password using bcrypt.
   * @param password the plain text password to hash
   * @returns a Promise resolving to the hashed password
   */
  async hashPassword(password: string) {
    const saltRounds = 10;
    return bcrypt.hash(password, saltRounds);
  }

  /**
   * Compare a plain text password with a hashed one.
   * If the hashed password is not set, return false immediately.
   * @param plainPassword the plain text password to compare
   * @param hashedPassword the hashed password to compare with
   * @returns a boolean indicating whether the passwords match
   */
  async comparePasswords(plainPassword: string, hashedPassword: string) {
    if (!hashedPassword) {
      return false;
    }
    return bcrypt.compare(plainPassword, hashedPassword);
  }
}

export default EncryptService;
