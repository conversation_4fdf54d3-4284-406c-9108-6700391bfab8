import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

import { PutObjectCommand, S3Client } from '@aws-sdk/client-s3';
import { from, map, Observable, of, switchMap, toArray } from 'rxjs';
import { v4 as Uuid } from 'uuid';

type File = Express.Multer.File;
type Response = { [k: string]: any };

@Injectable()
class S3Service {
  /** Client de comunicação com o S3 */
  #s3: S3Client;

  constructor(private readonly config: ConfigService) {
    // define as configuracões do AWS S3
    const accessKeyId = this.config.getOrThrow('aws.s3.access-key');
    const secretAccessKey = this.config.getOrThrow('aws.s3.secret-key');

    // cria o client de comunicação com o bucket
    this.#s3 = new S3Client({ credentials: { accessKeyId, secretAccessKey } });
  }

  upload(files: File, folder: string): Observable<Response>;
  upload(files: File[], folder: string): Observable<Response[]>;

  /**
   * Envia determinados arquivos para o S3 e registra suas informações no banco de dados.
   *
   * @param files arquivos que serão registrados.
   * @param folder diretório onde os arquivos serão armazenados no S3.
   */
  upload(
    files: File | File[],
    folder: string = '',
  ): Observable<Response | Response[]> {
    // itera entre os arquivos, enviado-os para o S3
    folder &&= folder + '/';

    if (!Array.isArray(files)) {
      return this.sendFile(files, folder) as Observable<Response>;
    }

    // retorna um observable contendo um array com cada arquivo
    return of(...files).pipe(
      switchMap((file) => this.sendFile(file, folder)),
      toArray(),
    ) as Observable<Response[]>;
  }

  protected sendFile(file: File, folder: string) {
    // Monta o objeto necessário para o envio
    const extension = file.originalname.split('.').splice(-1, 1);
    const key = `${folder}${Uuid()}.${extension}`;

    const params = new PutObjectCommand({
      Bucket: this.config.getOrThrow('aws.s3.bucket'),
      Key: key,
      Body: file.buffer,
      // ACL: 'public-read', // TODO: Implementar autorização!
      ContentType: file.mimetype,
      ContentDisposition: 'inline',
    });

    // faz o upload para o bucket
    const sendFile$ = from(this.#s3.send(params));

    // aguarda o upload ser concluído
    return sendFile$.pipe(
      map(({ $metadata }) => {
        // Usa os dados do S3 para armazenamento no banco de dados
        return {
          fileName: file.originalname,
          key,
          ...$metadata,
        };
      }),
    );
  }
}

export default S3Service;
