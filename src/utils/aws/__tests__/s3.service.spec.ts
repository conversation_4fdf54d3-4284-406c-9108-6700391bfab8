import { Readable } from 'stream';

import { ConfigService } from '@nestjs/config';
import { Test, TestingModule } from '@nestjs/testing';

import S3Service from '../s3.service';

jest.mock('@aws-sdk/client-s3', () => ({
  PutObjectCommand: jest.fn().mockImplementation((params) => params),

  S3Client: jest.fn().mockImplementation(() => ({
    send: jest.fn().mockResolvedValue({
      $metadata: {
        key: 'test-key',
        etag: 'test-etag',
        bucket: 'test-bucket',
      },
    }),
  })),
}));

describe('S3Service', () => {
  let service: S3Service;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        S3Service,
        {
          provide: ConfigService,
          useValue: {
            getOrThrow: jest.fn().mockImplementation((key) => {
              if (key === 'aws.s3.access-key') return 'test-access-key';
              if (key === 'aws.s3.secret-key') return 'test-secret-key';
              if (key === 'aws.s3.bucket') return 'test-bucket';
              throw new Error(`Unknown config key: ${key}`);
            }),
          },
        },
      ],
    }).compile();

    service = module.get<S3Service>(S3Service);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('upload', () => {
    it('should upload a single file', (done) => {
      const folder = 'test-folder';
      const file: Express.Multer.File = {
        originalname: 'test-file.txt',
        buffer: Buffer.from('Hello World!'),
        mimetype: 'text/plain',
        fieldname: '',
        encoding: '',
        size: 0,
        stream: Readable.from('Hello World!'),
        destination: '',
        filename: '',
        path: '',
      };

      service.upload(file, folder).subscribe((result) => {
        expect(result).toEqual({
          fileName: 'test-file.txt',
          bucket: 'test-bucket',
          key: 'test-key',
          etag: 'test-etag',
        });
        done();
      });
    });

    it.skip('should upload multiple files', (done) => {
      const folder = 'test-folder';
      const files: Express.Multer.File[] = [
        {
          originalname: 'test-file1.txt',
          buffer: Buffer.from('Hello World!'),
          mimetype: 'text/plain',
          fieldname: '',
          encoding: '',
          size: 0,
          stream: Readable.from('Hello World!'),
          destination: '',
          filename: '',
          path: '',
        },
        {
          originalname: 'test-file2.txt',
          buffer: Buffer.from('Hello World!'),
          mimetype: 'text/plain',
          fieldname: '',
          encoding: '',
          size: 0,
          stream: Readable.from('Hello World!'),
          destination: '',
          filename: '',
          path: '',
        },
      ];

      service.upload(files, folder).subscribe((result) => {
        console.log(result);
        expect(result).toEqual([
          {
            fileName: 'test-file1.txt',
            bucket: 'test-bucket',
            key: 'test-key',
            etag: 'test-etag',
          },
          {
            fileName: 'test-file2.txt',
            bucket: 'test-bucket',
            key: 'test-key',
            etag: 'test-etag',
          },
        ]);

        done();
      });
    });
  });

  describe('sendFile', () => {
    it('should send a file to S3', (done) => {
      const folder = 'test-folder';
      const file: Express.Multer.File = {
        originalname: 'test-file.txt',
        buffer: Buffer.from('Hello World!'),
        mimetype: 'text/plains',
        fieldname: '',
        encoding: '',
        size: 0,
        stream: Readable.from('Hello World!'),
        destination: '',
        filename: '',
        path: '',
      };

      service['sendFile'](file, folder).subscribe((result) => {
        expect(result).toEqual({
          fileName: 'test-file.txt',
          bucket: 'test-bucket',
          key: 'test-key',
          etag: 'test-etag',
        });
        done();
      });
    });
  });
});
