import {
  ArgumentsHost,
  Catch,
  ExceptionFilter,
  HttpException,
} from '@nestjs/common';

import type { Request, Response } from 'express';

import NewRelicLogDto from './new-relic/dto/new-relic-log.dto';
import NewRelicLogger from './new-relic/new-relic.logger';
import * as bcrypt from 'bcrypt';

@Catch(HttpException)
class HttpExceptionFilter implements ExceptionFilter {
  constructor(private readonly logger: NewRelicLogger) {}

  async catch(exception: HttpException, host: ArgumentsHost): Promise<void> {
    const ctx = host.switchToHttp();
    const req = ctx.getRequest<Request>();
    const payload = req.body;

    // Função auxiliar para criptografar senhas
    const hashPassword = async (
      password: string | undefined,
    ): Promise<string | undefined> => {
      if (password) {
        return await bcrypt.hash(password, 10);
      }
      return undefined;
    };

    // Criptografa as senhas, se existirem
    payload.password = await hashPassword(payload.password);
    payload.newPassword = await hashPassword(payload.newPassword);

    const clientIp = req.headers['X-Forwarded-For'] as string | undefined;
    const log: NewRelicLogDto = {
      ip: clientIp || req.ips!,
      action: 'http-exception-filter',
      route: req.url,
      httpStatusCode: exception.getStatus(),
      origin: 'aliviae',
      method: req.method,
      response: exception.getResponse(),
      payload: payload,
    };

    this.logger.log('error', log);

    ctx
      .getResponse<Response>()
      .status(exception.getStatus())
      .json({ details: exception.getResponse() });
  }
}

export default HttpExceptionFilter;
