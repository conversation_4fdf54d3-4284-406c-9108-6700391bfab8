import { Injectable, Logger, LogLevel } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

import NewRelicLogDto from './dto/new-relic-log.dto';
import axios, { AxiosInstance } from 'axios';

@Injectable()
class NewRelicLogger {
  readonly http: AxiosInstance;
  readonly #logger = new Logger(NewRelicLogger.name);
  readonly #env: 'development' | 'production' = 'development';

  constructor(private readonly config: ConfigService) {
    this.#env = this.config.get('app.env', 'development');

    this.http = axios.create({
      baseURL: 'https://log-api.newrelic.com/log/v1',
      headers: {
        'Content-Type': 'application/json',
        'Api-Key': config.get('new-relic.key'),
      },
    });
  }

  log(logtype: LogLevel, message: NewRelicLogDto) {
    // mostra no console o log que está sendo enviado
    this.#logger.log(logtype, { message });

    // envia o log para a New Relic via HTTP
    this.http
      .post('', { logtype, env: this.#env, message })
      .then(() => this.#logger.log('Log enviado!'))
      .catch((error) => {
        this.#logger.error('Erro ao enviar log para New Relic', error);
      });
  }
}

export default NewRelicLogger;
