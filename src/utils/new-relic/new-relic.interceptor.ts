import {
  CallH<PERSON><PERSON>,
  ExecutionContext,
  Injectable,
  NestInterceptor,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

import { Observable, tap } from 'rxjs';

import newrelic from 'newrelic';
import type { Request } from 'express';

import NewRelicLogger from './new-relic.logger';
import NewRelicLogDto from './dto/new-relic-log.dto';
import EncryptService from '@/utils/encrypt/encrypt.service';

@Injectable()
class NewRelicInterceptor implements NestInterceptor {
  constructor(
    private readonly logger: NewRelicLogger,
    private readonly config: ConfigService,
    private readonly encrypt: EncryptService,
  ) {}

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const now = Date.now();
    const isLocal = this.config.get('app.env') === 'local';

    const handler = context.getHandler().name;
    const req = context.switchToHttp().getRequest<Request>();

    // ignora logs de inicialização do NestJS
    if (handler === 'RouterExplorer') return next.handle();

    // não realiza o envio em desenvolvimento local
    if (isLocal) return next.handle();

    return newrelic.startWebTransaction(req.url, () => {
      const transaction = newrelic.getTransaction();

      // identifica o usário que faz a requisição
      if (req.user) newrelic.setUserID(req.user.cpf);

      return next.handle().pipe(
        tap(async (response) => {
          if (!isLocal && typeof response.payload === 'string') {
            response = await this.encrypt.decryptJWE(response.payload);
          }

          // adiciona a duração da requisição ao New Relic
          newrelic.recordMetric(`${handler}.duration`, Date.now() - now);

          // monta o objeto que será enviado ao new relic
          const { url, method, body } = req;
          const clientIp = req.headers['X-Forwarded-For'] as string | undefined;
          const log = new NewRelicLogDto(
            clientIp || req.ips!,
            handler,
            url,
            response.statusCode,
            'aliviae',
            method,
            { ...response.payload, duration: Date.now() - now },
            body,
          );
          this.logger.log('log', log);

          return transaction.end();
        }),
      );
    });
  }
}

export default NewRelicInterceptor;
