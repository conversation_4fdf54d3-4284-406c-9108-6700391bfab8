/**
 * DTO que define a estrutura de logs que são enviados ao New Relic no caso de algum
 * problema ou para fins de monitoramento.
 */
class NewRelicLogDto {
  constructor(
    // Identifica o IP de origem da requisição
    public readonly ip: string | string[],
    // Identifica o arquivo/rota de origem do log
    public readonly action: string,
    // Rota que originou o log
    public readonly route: string,
    // Status code resultante da requisição
    public readonly httpStatusCode: number,
    // Produto de origem do Log "aliviae"
    public readonly origin: string,
    // Método da requisição que originou o log
    public readonly method: string,
    // Dado da response que originou o log
    public readonly response?: unknown,
    // Dado da requisição que originou o log
    public readonly payload?: unknown,
  ) {}
}

export default NewRelicLogDto;
