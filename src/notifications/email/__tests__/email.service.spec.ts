import { Test, TestingModule } from '@nestjs/testing';
import { ConfigService } from '@nestjs/config';

import EmailService from '../email.service';

import CustomTransporter from '../transporters/custom.transporter';

describe('EmailService', () => {
  let service: EmailService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [EmailService, ConfigService, CustomTransporter],
    }).compile();

    service = module.get<EmailService>(EmailService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });
});
