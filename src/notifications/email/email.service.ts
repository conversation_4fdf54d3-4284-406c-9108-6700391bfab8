import fs from 'fs/promises';

import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

import { from } from 'rxjs';

import Handlebars from 'handlebars';
import { type Transporter } from 'nodemailer';
import type Mail from 'nodemailer/lib/mailer';

import CustomTransporter from './transporters/custom.transporter';

type ResetPasswordData = { name: string; email: string; code: string };
type WelcomeData = { name: string; email: string; appName: string; appUrl: string };

type MailContent = {
  'reset-password': ResetPasswordData;
  'welcome': WelcomeData;
};

@Injectable()
class EmailService {
  private transporter: Transporter;
  readonly #logger = new Logger(EmailService.name);

  constructor(
    private readonly config: ConfigService,
    private readonly transporterService: CustomTransporter,
  ) {
    this.transporter = this.transporterService.getTransporter();
  }

  /**
   * Busca o template de e-mail que será utilizado no envio.
   *
   * @todo Migrar a logica para busca no S3.
   * @param template Nome do template que será utilizado.
   */
  async getTemplate(template: keyof MailContent) {
    // busca pelo arquivo no diretório de templates
    const path = `src/resources/mail/${template}.hbs`;

    // retorna o conteúdo do arquivo para formatação
    const fileBuffer = await fs.readFile(path);

    return fileBuffer.toString();
  }

  /**
   * Preenche os campos definidos no template para prepará-lo para o disparo.
   *
   * @param template Nome do template que será utilizado.
   * @param data Informações que serão utilizadas no preenchimento.
   * @returns O conteúdo do e-mail pronto para disparo.
   */
  async compileTemplate<T extends keyof MailContent>(
    template: T,
    data: MailContent[T],
  ) {
    const html = await this.getTemplate(template);

    // calcula o tempo de compilação
    const start = performance.now();
    const compiled = Handlebars.compile(html);
    const end = (performance.now() - start).toFixed(3);
    this.#logger.debug(`Compilação do template "${template}": ${end}ms`);

    return compiled(data);
  }

  /**
   * Envia o e-mail de redefinição de senha para o usuário que o solicitou.
   *
   * @param data Informações que serão preenchidas no template de e-mail.
   */
  async sendResetPassword(data: ResetPasswordData) {
    const sender = this.config.getOrThrow('notifications.email.sender');
    const options: Mail.Options = {
      to: data.email,
      subject: 'Redefinir senha',
      html: await this.compileTemplate('reset-password', data),
      from: `Aliviaê <${sender}>`,
    };

    // dispara o e-mail
    this.send(options);
  }

  /**
   * Envia o e-mail de boas-vindas para o usuário recém-cadastrado.
   *
   * @param data Informações que serão preenchidas no template de e-mail.
   */
  async sendWelcome(data: WelcomeData) {
    const sender = this.config.getOrThrow('notifications.email.sender');
    const options: Mail.Options = {
      to: data.email,
      subject: `Bem-vindo(a) ao ${data.appName}!`,
      html: await this.compileTemplate('welcome', data),
      from: `Aliviaê <${sender}>`,
    };

    // dispara o e-mail
    this.send(options);
  }

  /**
   * Realiza o disparo de e-mail em background, sobrescrevendo o Observable.
   * @param options Opções de disparo do e-mail.
   * @protected
   */
  protected send(options: Mail.Options) {
    from(this.transporter.sendMail(options)).subscribe({
      next: () => this.#logger.log('E-mail enviado com sucesso!'),
      error: (error) => this.#logger.error(error.message),
    });
  }
}

export default EmailService;