type Transporter = import('nodemailer').Transporter;
type SMTPPoolOptions = import('nodemailer/lib/smtp-pool').Options;

/**
 * Interface que define os contratos a serem implementados pelos serviços
 * de disparo de e-mail.
 */
interface AppTransporter {
  /**
   * Configura o transporter a ser utilizado e retorna sua instância.
   *
   * @param options Configurações adicionais do transporter.
   * @returns A instância do transporter pronta para ser utilizada.
   */
  getTransporter(options?: Partial<SMTPPoolOptions>): Transporter;
}
