import { ConfigService } from '@nestjs/config';
import { Injectable } from '@nestjs/common';

import nodemailer from 'nodemailer';
import type SMTPPool from 'nodemailer/lib/smtp-pool';

@Injectable()
class CustomTransporter implements AppTransporter {
  constructor(private readonly config: ConfigService) {}

  getTransporter(options: Partial<SMTPPool.Options> = {}) {
    const config: SMTPPool.Options = {
      pool: true,
      secure: false,
      host: this.config.get('notifications.email.host'),
      port: this.config.get('notifications.email.port', 587),
      auth: {
        user: this.config.get('notifications.email.auth.user'),
        pass: this.config.get('notifications.email.auth.password'),
      },
      ...options,
    };

    return nodemailer.createTransport(config);
  }
}

export default CustomTransporter;
