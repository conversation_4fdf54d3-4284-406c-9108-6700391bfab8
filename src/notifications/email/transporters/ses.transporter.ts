import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

import nodemailer from 'nodemailer';
import type SMTPPool from 'nodemailer/lib/smtp-pool';

@Injectable()
class SesTransporter implements AppTransporter {
  constructor(private readonly config: ConfigService) {}

  getTransporter(options: Partial<SMTPPool.Options> = {}) {
    const config: SMTPPool.Options = {
      pool: true,
      secure: true,
      host: `email-smtp.${this.config.get('aws.ses.region')}.amazonaws.com`,
      port: this.config.get('aws.ses.port', 587),
      auth: {
        user: this.config.get('aws.ses.access-key'),
        pass: this.config.get('aws.ses.secret-key'),
      },
      ...options,
    };

    return nodemailer.createTransport(config);
  }
}

export default SesTransporter;
