import FirebaseService from '../firebase.service';

describe('FirebaseService', () => {
  let service: FirebaseService;
  let mockSend: jest.Mock;

  beforeEach(() => {
    mockSend = jest.fn();

    const mockMessaging = { send: mockSend };

    service = new FirebaseService(mockMessaging as any);
  });

  describe('sendMessage', () => {
    it('deve enviar mensagem com sucesso', (done) => {
      mockSend.mockResolvedValue('message-id-123');

      service.sendMessage('test-token', { title: 'Test' }).subscribe({
        complete: () => {
          expect(mockSend).toHaveBeenCalledWith({
            token: 'test-token',
            data: { title: 'Test' },
          });
          done();
        },
        error: done.fail,
      });
    });

    it('deve tratar erro e retornar void', (done) => {
      mockSend.mockRejectedValue(new Error('Firebase error'));

      service.sendMessage('test-token', { title: 'Test' }).subscribe({
        complete: () => done(),
        error: done.fail,
      });
    });
  });

  describe('sendAppointmentNotification', () => {
    it('deve enviar notificação para status COMPLETED', (done) => {
      mockSend.mockResolvedValue('notification-id');

      service.sendAppointmentNotification('COMPLETED', 'token').subscribe({
        complete: () => {
          expect(mockSend).toHaveBeenCalledWith({
            token: 'token',
            data: {
              title: 'Status da Consulta',
              body: 'Sua consulta foi concluída',
            },
          });
          done();
        },
        error: done.fail,
      });
    });

    it('deve enviar notificação para status CANCELLED', (done) => {
      mockSend.mockResolvedValue('notification-id');

      service.sendAppointmentNotification('CANCELLED', 'token').subscribe({
        complete: () => {
          expect(mockSend).toHaveBeenCalledWith({
            token: 'token',
            data: {
              title: 'Status da Consulta',
              body: 'Sua consulta foi cancelada',
            },
          });
          done();
        },
        error: done.fail,
      });
    });

    it('deve enviar notificação para status SCHEDULED (default)', (done) => {
      mockSend.mockResolvedValue('notification-id');

      service.sendAppointmentNotification('SCHEDULED', 'token').subscribe({
        complete: () => {
          expect(mockSend).toHaveBeenCalledWith({
            token: 'token',
            data: {
              title: 'Status da Consulta',
              body: 'Sua consulta foi atualizada',
            },
          });
          done();
        },
        error: done.fail,
      });
    });

    it('deve enviar notificação para status desconhecido (default)', (done) => {
      mockSend.mockResolvedValue('notification-id');

      service.sendAppointmentNotification('UNKNOWN', 'token').subscribe({
        complete: () => {
          expect(mockSend).toHaveBeenCalledWith({
            token: 'token',
            data: {
              title: 'Status da Consulta',
              body: 'Sua consulta foi atualizada',
            },
          });
          done();
        },
        error: done.fail,
      });
    });

    it('deve tratar erro de notificação sem lançar exceção', (done) => {
      mockSend.mockRejectedValue(new Error('Firebase error'));

      service.sendAppointmentNotification('COMPLETED', 'token').subscribe({
        complete: () => done(),
        error: done.fail,
      });
    });
  });
});
