import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';

import { initializeApp, cert } from 'firebase-admin/app';
import { getMessaging } from 'firebase-admin/messaging';
import type { ServiceAccount } from 'firebase-admin';

import FirebaseService from './firebase.service';

@Module({
  imports: [ConfigModule],
  providers: [
    {
      provide: 'FIREBASE_MESSAGING',
      inject: [ConfigService],
      useFactory: (config: ConfigService) => {
        const serviceAccount: ServiceAccount = {
          projectId: config.getOrThrow('notifications.firebase.projectId'),
          clientEmail: config.getOrThrow('notifications.firebase.clientEmail'),
          privateKey: config
            .getOrThrow('notifications.firebase.privateKey')
            .replace(/\\n/g, '\n'),
        };

        const app = initializeApp({
          credential: cert(serviceAccount),
        });

        return getMessaging(app);
      },
    },
    FirebaseService,
  ],
  exports: [FirebaseService],
})
export default class FirebaseModule {}
