import { Inject, Injectable, Logger } from '@nestjs/common';
import { catchError, from, ignoreElements, Observable, of, tap } from 'rxjs';
import { Messaging } from 'firebase-admin/messaging';

@Injectable()
export default class FirebaseService {
  readonly #logger = new Logger(FirebaseService.name);

  constructor(
    @Inject('FIREBASE_MESSAGING')
    private readonly messaging: Messaging,
  ) {}

  /**
   * Envia uma mensagem para um dispositivo mobile, utilizando firebase.
   *
   * @param token Token do dispositivo mobile.
   * @param data Dados a serem enviados na mensagem.
   *
   * @returns Observable void.
   */
  sendMessage(token: string, data: Record<string, string>): Observable<void> {
    const message = {
      token,
      data,
    };

    return from(this.messaging.send(message)).pipe(
      tap((response) => {
        this.#logger.log('Mensagem Firebase enviada com sucesso:', response);
      }),
      ignoreElements(),
      catchError((error) => {
        this.#logger.error('Erro ao enviar mensagem Firebase:', error);
        return of();
      }),
    );
  }

  /**
   * Envia uma notificação para um beneficiário com o status da consulta
   *
   * @param status Status da consulta (COMPLETED, CANCELED, SCHEDULED)
   * @param token Token do dispositivo do beneficiário
   */
  sendAppointmentNotification(status: string, token: string): Observable<void> {
    const messageData = {
      title: 'Status da Consulta',
      body: `Sua consulta foi ${status === 'COMPLETED' ? 'concluída' : status === 'CANCELLED' ? 'cancelada' : 'atualizada'}`,
    };

    return this.sendMessage(token, messageData);
  }
}
