import { Injectable, NotFoundException } from '@nestjs/common';

import PrismaService from '@/utils/prisma/prisma.service';
import { CreateAddressDto } from './dto/create-address.dto';
import { Prisma } from '@prisma/client';
import { TextFormatter } from '@/utils/text-formatter.util';

@Injectable()
class AddressService {
  constructor(private readonly prisma: PrismaService) {}

  /**
   * Retorna o ID do endereo do usuário.
   *
   * @param userId O ID do usuário.
   * @param ctx Um client de transação do Prisma.
   * @returns O ID do endereo do usuário, caso ele possua um. Caso contrário, retorna undefined.
   */
  async getAddress(
    userId: number,
    ctx: Prisma.TransactionClient = this.prisma,
  ): Promise<number | undefined> {
    const address = await ctx.userAddresses.findFirst({
      where: { userId },
      select: { addressId: true },
    });

    return address?.addressId;
  }

  /**
   * Retorna o código da cidade com base no seu nome e no estado.
   * Lança um erro caso a cidade não seja encontrada.
   *
   * @param data Dados da cidade.
   * @returns Código IBGE da cidade.
   */
  async getCityCode(
    data: { city: string; state: string },
    ctx: Prisma.TransactionClient = this.prisma,
  ): Promise<string> {
    const city = await ctx.cities.findFirst({
      where: {
        name: data.city,
        stateAcronym: data.state.toUpperCase(),
      },
      select: {
        code: true,
      },
    });

    if (!city) throw new NotFoundException('Cidade não encontrada!');
    return city.code;
  }

  /**
   * Salva ou atualiza um endereço para o usuário.
   *
   * @param data Novas informa es do endereço.
   * @param user Informa es do usuário para relacionar ao endereço.
   * @returns ID do endereço.
   */
  async save(
    data: CreateAddressDto,
    userId: number,
    ctx: Prisma.TransactionClient = this.prisma,
  ): Promise<number> {
    const cityCode = await this.getCityCode(
      { city: data.city, state: data.state },
      ctx,
    );

    const addressId = await this.getAddress(userId, ctx);

    const addressData = {
      street: TextFormatter.toTitleCase(data.street),
      number: data.number,
      neighborhood: TextFormatter.toTitleCase(data.neighborhood),
      zipCode: data.zipCode,
      complement: data.complement
        ? TextFormatter.toSentenceCase(data.complement)
        : null,
      city: { connect: { code: cityCode } },
    };

    // Atualiza o endereço existente
    if (addressId) {
      const updatedAddress = await ctx.addresses.update({
        where: { id: addressId },
        data: addressData,
      });
      return updatedAddress.id;
    }

    // Cria um novo endereço
    const createdAddress = await ctx.addresses.create({
      data: addressData,
    });

    await ctx.userAddresses.create({
      data: { userId, addressId: createdAddress.id },
    });

    return createdAddress.id;
  }
}

export default AddressService;
