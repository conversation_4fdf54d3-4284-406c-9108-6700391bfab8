import {
  IsNotEmpty,
  IsOptional,
  IsString,
  Matches,
  MaxLength,
  MinLength,
} from 'class-validator';

import { ApiProperty } from '@nestjs/swagger';

export class CreateAddressDto {
  @ApiProperty({ description: 'Rua do endereço' })
  @IsNotEmpty()
  @IsString()
  @MaxLength(255)
  street: string;

  @ApiProperty({ description: 'Número do endereço' })
  @IsNotEmpty()
  @IsString()
  @MaxLength(7)
  number: string;

  @ApiProperty({
    description: 'zipCode do endereço, deve ter 8 caracteres numéricos',
  })
  @IsNotEmpty()
  @IsString()
  @MinLength(8)
  @MaxLength(8)
  @Matches(/^[0-9]{8}$/, {
    message: 'zipCode deve conter exatamente 8 dígitos numéricos',
  })
  zipCode: string;

  @ApiProperty({
    description: 'Complemento do endereço',
    required: false,
    nullable: true,
  })
  @IsOptional()
  @IsString()
  @MaxLength(100)
  complement: string | null;

  @ApiProperty({ description: 'Cidade do endereço' })
  @IsNotEmpty()
  @IsString()
  @MaxLength(100)
  city: string;

  @ApiProperty({ description: 'Sigla do estado' })
  @IsNotEmpty()
  @IsString()
  @MinLength(2)
  @MaxLength(2)
  state: string;

  @ApiProperty({ description: 'Bairro do endereço' })
  @IsNotEmpty()
  @IsString()
  @MaxLength(255)
  neighborhood: string;
}
