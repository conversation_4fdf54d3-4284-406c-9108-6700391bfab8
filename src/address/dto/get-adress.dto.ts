import { ApiProperty } from '@nestjs/swagger';

class GetAddressDto {
  @ApiProperty({ description: 'Rua do endereço' })
  street: string;

  @ApiProperty({ description: 'Número do endereço' })
  number: string;

  @ApiProperty({ description: 'CEP do endereço' })
  zipCode: string;

  @ApiProperty({ description: 'Complemento do endereço', nullable: true })
  complement: string | null;

  @ApiProperty({ description: 'Cidade do endereço' })
  city: string;

  @ApiProperty({ description: 'Estado do endereço' })
  state: string;

  @ApiProperty({ description: 'Bairro do endereço' })
  neighborhood: string;
}

export default GetAddressDto;
