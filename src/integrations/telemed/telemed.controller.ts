import {
  BadRequestException,
  Body,
  Controller,
  Delete,
  Get,
  InternalServerErrorException,
  Logger,
  Param,
  ParseIntPipe,
  Post,
  Query,
  Request,
  UnauthorizedException,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';

import {
  ApiBearerAuth,
  ApiBody,
  ApiOperation,
  ApiParam,
  ApiQuery,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';

import { firstValueFrom, from, map, of, switchMap } from 'rxjs';

import TelemedGuard from './telemed.guard';
import TelemedService from './telemed.service';
import TopmedService from './topmed/topmed.service';
import TopmedInterceptor from './topmed/topmed.interceptor';

import UserService from '@/user/user.service';

import GetPhysicianDto from './topmed/dto/get-physician.dto';
import JoinAppointmentDto from './topmed/dto/join-appointment.dto';
import ScheduleAppointmentDto from './topmed/dto/schedule-appointment.dto';
import GetAppointmentDto, {
  AppointmentDto,
} from './topmed/dto/get-appointment.dto';
import GetAppointmentQueueDto from './topmed/dto/get-appointment-queue.dto';
import CancelAppointmentQueueDto from './topmed/dto/cancel-appointment-queue.dto';
import CancelAppointmentDto from './topmed/dto/cancel-appointment.dto';
import CreateTelemedDependentDto from './topmed/dto/create-dependent.dto';
import GetPhysicianSpecialityDto from '@/integrations/telemed/topmed/dto/get-physician-speciality.dto';

@ApiBearerAuth()
@ApiTags('integrations')
@ApiResponse({
  status: 503,
  description: 'API de integração com a TopMed indisponível',
})
@UseGuards(TelemedGuard)
@UseInterceptors(TopmedInterceptor)
@Controller({ path: 'integrations/telemed', version: '1' })
class TelemedController {
  readonly #logger = new Logger(TelemedController.name);

  constructor(
    private readonly user: UserService,
    private readonly telemed: TelemedService,
    private readonly topmed: TopmedService,
  ) {}

  @ApiResponse({
    status: 200,
    description: 'Beneficiário encontrado na base de dados TopMed',
  })
  @ApiOperation({ summary: 'Busca o beneficiário na base de dados TopMed' })
  @Get('/beneficiary')
  getBeneficiary(@Request() req: Express.Request) {
    const { cpf } = req.user!;
    return this.topmed.getPerson(cpf);
  }

  @ApiResponse({
    status: 201,
    description: 'Beneficiário registrado na base de dados TopMed',
  })
  @ApiResponse({
    status: 400,
    description: 'CPF inválido ou não cadastrado na base Aliviaê',
  })
  @ApiOperation({ summary: 'Registra o beneficiário na base TopMed' })
  @Post('/beneficiary')
  registerBeneficiary(@Request() req: Express.Request) {
    const { cpf } = req.user!;

    // TODO: adicionar alguma verificação para registrar o beneficiário usando o corpo da requisição
    // TODO: migrar rota para o painel administrativo. Deverá cadastrar somente titulares

    return from(this.user.findByCpf(cpf)).pipe(
      switchMap((user) => {
        if (!user) throw new BadRequestException('Usuário não encontrado');

        // registra o beneficiário na Topmed
        return from(this.topmed.registerCurrentBeneficiary(cpf)).pipe(
          switchMap((registeredUser) => {
            // busca os dados do beneficiário na Topmed
            return from(this.topmed.getPerson(cpf)).pipe(
              switchMap((person) => {
                const telemedUser = {
                  id: req.user!.id,
                  telemedId: person.Id.toString(),
                  registeredAt: new Date(registeredUser.ProcessedDate),
                };

                // cria o beneficiário na base de dados
                return from(this.telemed.createTelemedUser(telemedUser));
              }),
            );
          }),
        );
      }),
    );
  }

  @ApiResponse({
    status: 201,
    description: 'Dependente registrado na base de dados TopMed',
  })
  @ApiResponse({
    status: 400,
    description: 'CPF inválido ou não cadastrado na base Aliviaê',
  })
  @ApiBody({
    type: CreateTelemedDependentDto,
    description: 'Informações do dependente a ser cadastrado',
  })
  @ApiOperation({ summary: 'Registra um dependente na base TopMed' })
  @Post('/beneficiary/:holderId/dependents')
  async registerDependent(
    @Param('holderId', ParseIntPipe) holderId: number,
    @Body() body: CreateTelemedDependentDto,
    @Request() req: Express.Request,
  ) {
    const { cpf } = req.user!;

    // TODO: rever regra de permissão nesses endpoints
    if (holderId !== req.user!.id) {
      throw new UnauthorizedException(
        'O ID do beneficiário deve ser o mesmo do token',
      );
    }
    try {
      // verifica se o dependente pertence ao titular
      await this.telemed.getDependent(req.user!.id, body.id);

      // converte o corpo da requisição para o formato TopMed
      const provider = await this.topmed.convertPersonToProvider(body, cpf);

      const registeredUser = await firstValueFrom(
        this.topmed.registerDependent(holderId, provider),
      );

      // busca dados do usuário na topmed
      const registeredAt = new Date(registeredUser.ProcessedDate);
      const telemedUser = await firstValueFrom(this.topmed.getPerson(body.cpf));

      const user = {
        id: body.id,
        telemedId: telemedUser.Id.toString(),
        registeredAt,
      };

      // cria o beneficiário na base de dados
      return await this.telemed.createTelemedUser(user);
    } catch (error) {
      this.#logger.error(error.message);
      throw error;
    }
  }

  @ApiQuery({
    name: 'date',
    description: 'Data da consulta à ser agendada',
    example: '2024-10-11T05:00:00',
  })
  @ApiQuery({
    name: 'specialityId',
    description: 'ID da especialidade dos médicos nas base da TopMed',
    enum: ['66', '96'],
  })
  @ApiResponse({
    status: 200,
    description: 'Lista de médicos e seus horários disponíveis',
    type: GetPhysicianDto,
  })
  @ApiOperation({
    summary: 'Lista os médicos disponíveis para agendamento de consulta',
  })
  @Get('/physicians')
  getAvailablePhysicians(
    @Query('date') date: string,
    @Query('specialityId') specialityId: '66' | '96' | string,
    @Request() req: Express.Request,
  ) {
    const { id, cpf } = req.user!;
    const fmtDate = new Date(date);
    // corrige o timezone da data enviada
    fmtDate.setMinutes(fmtDate.getMinutes() - fmtDate.getTimezoneOffset());

    // limita a especialidade quando o usuário é dependente
    const verifyHolder$ = from(this.user.isHolder(id));

    // retorna os horários disponíveis de determinada as especialidades
    return verifyHolder$.pipe(
      switchMap((isHolder) => {
        if (!isHolder && specialityId !== '66') return of([]);

        return this.topmed.listPhysicians(cpf, specialityId, fmtDate);
      }),
    );
  }

  @ApiResponse({
    status: 200,
    description: 'Lista de especialidades disponíveis para consulta',
    type: [GetPhysicianSpecialityDto],
  })
  @ApiOperation({
    summary: 'Lista as especialidades disponíveis para agendamento de consulta',
  })
  @Get('/physicians/specialities')
  listSpecialities(@Request() req: Express.Request) {
    const { cpf } = req.user!;

    return this.topmed.listSpecialities(cpf);
  }

  @ApiResponse({
    status: 200,
    description: 'Lista de consultas do beneficiário',
    type: GetAppointmentDto,
  })
  @ApiOperation({ summary: 'Lista as consultas do beneficiário' })
  @Get('/appointments')
  async listAppointments(
    @Request() req: Express.Request,
  ): Promise<GetAppointmentDto> {
    const user = await this.user.findById(req.user!.id);
    if (!user) throw new BadRequestException('Usuário nao encontrado');

    // busca as consultas do beneficiário
    const appointments = await firstValueFrom(
      this.topmed.listAppointments(req.user!.cpf),
    );

    return {
      data: appointments.map((appointment) => ({
        id: appointment.Hash,
        date: new Date(appointment.DataDaConsulta),
        imageUrl: appointment.CaminhoImagemCompleto,
        symptoms: appointment.Sintomas,
        physician: {
          name: appointment.NomeDoMedico,
          speciality: appointment.Especialidade,
        },
        status: {
          id: appointment.Situacao,
          name: appointment.NomeDaSituacao,
        },
        name: user.name || '',
        cpf: req.user!.cpf,
        userId: user.id,
      })),
    };
  }

  @ApiResponse({
    status: 200,
    description: 'Lista de consultas dos dependentes',
    type: GetAppointmentDto,
  })
  @ApiOperation({ summary: 'Lista as consultas dos dependentes' })
  @Get('/appointments/dependents')
  async listDependentAppointments(
    @Request() req: Express.Request,
  ): Promise<GetAppointmentDto> {
    const appointments: AppointmentDto[] = [];
    const userCpf = req.user!.cpf;

    // busca os dependentes e remove o titular da lista
    const dependents = (await this.user.getUserDependents(req.user!.id)).filter(
      (dependent) => dependent.cpf !== userCpf,
    );
    // TODO: verificar se o usuário tem cadastro na telemed_users antes de buscar as consultas
    if (dependents.length === 0)
      throw new BadRequestException('Nenhum dependente cadastrado');

    await Promise.all(
      dependents.map(async (dependent) => {
        const dependentAppointments = await firstValueFrom(
          this.topmed.listAppointments(dependent.cpf),
        );

        appointments.push(
          ...dependentAppointments.map((appointment) => ({
            id: appointment.Hash,
            date: new Date(appointment.DataDaConsulta),
            imageUrl: appointment.CaminhoImagemCompleto,
            symptoms: appointment.Sintomas,
            physician: {
              name: appointment.NomeDoMedico,
              speciality: appointment.Especialidade,
            },
            status: {
              id: appointment.Situacao,
              name: appointment.NomeDaSituacao,
            },
            name: dependent.name || '',
            cpf: dependent.cpf,
            userId: dependent.id,
          })),
        );
      }),
    );

    return {
      data: appointments,
    };
  }

  @ApiBody({
    description: 'Informações para agendamento da consulta',
    type: ScheduleAppointmentDto,
  })
  @ApiResponse({
    status: 201,
    description: 'Informação da consulta agendada',
    type: AppointmentDto,
  })
  @ApiOperation({ summary: 'Agenda uma consulta para o beneficiário' })
  @Post('/appointments')
  async scheduleAppointment(
    @Body() body: ScheduleAppointmentDto,
    @Request() req: Express.Request,
  ): Promise<AppointmentDto> {
    const { cpf } = req.user!;
    const { appointmentId, symptoms } = body;

    //TODO: substituir por uma busca pelo usuário na tabela telemedUsers
    const user = await this.user.findById(req.user!.id);
    if (!user) throw new BadRequestException('Usuário nao encontrado');

    // agenda consulta
    const appointment = await firstValueFrom(
      this.topmed.scheduleAppointment(cpf, appointmentId, symptoms),
    );

    const appointmentDate = new Date(appointment.DataDaConsulta);

    // associa consulta ao beneficiário no banco de dados
    await firstValueFrom(
      this.telemed.linkAppointment(cpf, appointment.Hash, appointmentDate),
    );

    return {
      id: appointment.Hash,
      date: appointmentDate,
      imageUrl: appointment.CaminhoImagemCompleto,
      status: {
        id: appointment.Situacao,
        name: appointment.NomeDaSituacao,
      },
      symptoms: appointment.Sintomas,
      physician: {
        name: appointment.NomeDoMedico,
        speciality: appointment.Especialidade,
      },
      name: user.name || '',
      cpf: cpf,
      userId: user.id,
    };
  }

  @ApiBody({
    description: 'Informações para agendamento da consulta',
    type: ScheduleAppointmentDto,
  })
  @ApiResponse({
    status: 201,
    description: 'Informação da consulta agendada',
    type: AppointmentDto,
  })
  @ApiParam({
    name: 'dependentId',
    description: 'ID do dependente',
  })
  @ApiOperation({ summary: 'Agenda uma consulta para o dependente' })
  @Post('/appointments/:dependentId/dependents')
  async scheduleAppointmentDependent(
    @Param('dependentId', ParseIntPipe) dependentId: number,
    @Body() body: ScheduleAppointmentDto,
  ): Promise<AppointmentDto> {
    const { appointmentId, symptoms } = body;
    //TODO: substituir por uma busca pelo usuário na tabela telemedUsers
    const user = await this.user.findById(dependentId);
    if (!user) throw new BadRequestException('Usuário nao encontrado');

    // agenda consulta
    const appointment = await firstValueFrom(
      this.topmed.scheduleAppointment(user.cpf, appointmentId, symptoms),
    );

    const appointmentDate = new Date(appointment.DataDaConsulta);

    // associa consulta ao beneficiário no banco de dados
    await firstValueFrom(
      this.telemed.linkAppointment(user.cpf, appointment.Hash, appointmentDate),
    );

    return {
      id: appointment.Hash,
      date: appointmentDate,
      imageUrl: appointment.CaminhoImagemCompleto,
      status: {
        id: appointment.Situacao,
        name: appointment.NomeDaSituacao,
      },
      symptoms: appointment.Sintomas,
      physician: {
        name: appointment.NomeDoMedico,
        speciality: appointment.Especialidade,
      },
      name: user.name || '',
      cpf: user.cpf,
      userId: user.id,
    };
  }

  @ApiResponse({
    status: 200,
    description: 'Confirmação do cancelamento',
    type: CancelAppointmentQueueDto,
  })
  @ApiResponse({
    status: 404,
    description: 'Consulta ou usuário não encontrado',
  })
  @ApiResponse({
    status: 400,
    description: 'Consulta já cancelada',
  })
  @ApiBody({
    type: CancelAppointmentDto,
    description: 'Informações para cancelamento da consulta',
  })
  @ApiOperation({ summary: 'Solicita o cancelamento de uma consulta agendada' })
  @Delete('/appointments')
  async cancelAppointment(
    @Request() req: Express.Request,
    @Body() body: CancelAppointmentDto,
  ) {
    const { id, dependentId } = body;

    // busca o cpf do dependente validando se ele pertence ao titular
    const dependent = dependentId
      ? await this.telemed.getDependent(req.user!.id, dependentId)
      : null;

    // atribui cpf do titular ou do dependente
    const cpf = dependent || req.user!.cpf;

    // busca a consulta pelo externalId
    await firstValueFrom(this.telemed.getAppointmentByExternalId(cpf, id));

    try {
      // cancela consulta na base TopMed
      await firstValueFrom(this.topmed.cancelAppointment(id));

      // observable para cancelamento da consulta na base Aliviaê
      return await firstValueFrom(
        from(this.telemed.updateAppointment(id, cpf, 'CANCELLED')),
      );
    } catch (err) {
      const msg = 'Erro ao cancelar consulta do beneficiário';
      this.#logger.error(msg, err);
      throw new InternalServerErrorException(msg);
    }
  }

  @ApiBody({
    description: 'Informações para acesso à fila de atendimento',
    type: JoinAppointmentDto,
  })
  @ApiResponse({
    status: 201,
    description: 'Informações da consulta agendada',
    type: GetAppointmentQueueDto,
  })
  @ApiOperation({
    summary: 'Solicita a inclusão do beneficiário na fila de autoatendimento',
  })
  @Post('/appointments/queue')
  joinAppointmentQueue(
    @Body() body: JoinAppointmentDto,
    @Request() req: Express.Request,
  ) {
    const { ignoreExistent, symptoms, dependentCpf } = body;

    // por padrão utiliza o CPF do titular
    let getCpf$ = of(req.user!.cpf);

    // verifica se o CPF do dependente foi informado e se é realmente um dependente
    if (dependentCpf) {
      getCpf$ = from(this.user.findByCpf(dependentCpf)).pipe(
        // verifica se o usuário dependente foi cadastrado
        switchMap((user) => {
          if (!user) {
            throw new BadRequestException('Dependente não encontrado');
          }

          const holderId = req.user!.id;
          return from(this.user.findUserDependent(holderId, user.id));
        }),
        // verifica se o dependente pertence ao titular
        map((dependent) => {
          if (!dependent) {
            throw new BadRequestException('Dependente não encontrado');
          }

          return dependent.cpf;
        }),
      );
    }

    // determina se o CPF do titular ou depende será utilizado
    return getCpf$.pipe(
      // busca as informações do usuário na TopMed para realizar as validações
      switchMap((cpf) => this.topmed.getPerson(cpf)),
      // verifica se o beneficiário está na fila de atendimento
      switchMap((person) => {
        return (
          this.topmed
            .getHealthSelfServiceLink(person.Id)
            // formata o retorno para informar o CPF que será utilizado
            .pipe(map((response) => ({ response, cpf: person.Cpf })))
        );
      }),
      // se não estiver na fila de atendimento, solicita a inclusão
      switchMap(({ response, cpf }) => {
        if (!response || ignoreExistent) {
          return this.topmed.joinAppointmentQueue(cpf, symptoms);
        }

        return of<JoinQueueResponse>({
          Mensagem: 'Fila de autoatendimento reaproveitada',
          HashRequisicao: response.Id,
          LinkDirecionamento: response.UrlChat,
        });
      }),
    );
  }
}

export default TelemedController;
