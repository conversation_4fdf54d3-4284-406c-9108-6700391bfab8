import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { JwtModule } from '@nestjs/jwt';

import AuthModule from '@/auth/auth.module';
import UserModule from '@/user/user.module';

import TopmedModule from './topmed/topmed.module';
import TopmedService from './topmed/topmed.service';
import TelemedService from './telemed.service';
import TelemedController from './telemed.controller';

const JWTModule = JwtModule.registerAsync({
  imports: [ConfigModule],
  inject: [ConfigService],
  useFactory: async (config: ConfigService) => ({
    signOptions: { expiresIn: config.get<number>('app.jwt.expiration')! * 60 },
    secret: config.get<string>('app.jwt.secret'),
  }),
});

@Module({
  imports: [TopmedModule, UserModule, AuthModule, JWTModule],
  providers: [TelemedService, TopmedService, ConfigService],
  controllers: [TelemedController],
  exports: [TopmedService, TelemedService],
})
export default class TelemedModule {}
