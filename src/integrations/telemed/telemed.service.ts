import {
  BadRequestException,
  HttpException,
  Injectable,
  Logger,
  NotFoundException,
} from '@nestjs/common';

import { catchError, from, map, throwError } from 'rxjs';

import { Status } from '@prisma/client';

import PrismaService from '@/utils/prisma/prisma.service';

@Injectable()
class TelemedService {
  readonly #logger = new Logger(TelemedService.name);

  constructor(private readonly prisma: PrismaService) {}

  /**
   * Cria um novo usuário de telemedicina ou atualiza um existente.
   *
   * @param user Dados do usuário a ser criado ou atualizado.
   * @param registeredAt Data de registro do usuário.
   * @returns O novo usuário ou o usuário atualizado.
   * @throws {BadRequestException} Se o fornecedor da telemedicina não for encontrado.
   * @throws {BadRequestException} Se houver um erro ao criar ou atualizar o usuário.
   */

  async createTelemedUser(user: {
    id: number;
    registeredAt: Date;
    telemedId: string;
  }) {
    try {
      const registeredAt = user.registeredAt;

      // Corrige timezone
      registeredAt.setMinutes(
        registeredAt.getMinutes() - registeredAt.getTimezoneOffset(),
      );

      // busca o fornecedor da telemedicina
      const supplier = await this.prisma.suppliers.findFirst({
        where: {
          companies: {
            corporateName: 'Topmed Assistência a Saúde Ltda',
            cnpj: '05791085000197',
          },
        },
        select: {
          id: true,
        },
      });

      if (!supplier) throw new BadRequestException('Fornecedor nao encontrado');

      const supplierId = supplier?.id;

      return this.prisma.telemedUsers.upsert({
        where: {
          userId: user.id,
        },
        create: {
          userTelemedId: user.telemedId,
          userId: user.id,
          supplierId,
          registeredAt: registeredAt,
        },
        update: {
          userTelemedId: user.telemedId,
          userId: user.id,
          supplierId,
          registeredAt: registeredAt,
        },
      });
    } catch (error) {
      this.#logger.error(error);
      if (error instanceof HttpException) throw error;
    }
  }

  /**
   * Retorna o cpf do beneficiário dependente.
   *
   * @param holderId ID do beneficiário titular.
   * @param dependentId ID do beneficiário dependente.
   *
   * @returns CPF do beneficiário dependente
   */

  async getDependent(holderId: number, dependentId: number) {
    try {
      const dependent = await this.prisma.userDependents.findFirst({
        where: {
          holderId: holderId,
          dependentId: dependentId,
        },
      });

      if (!dependent)
        throw new NotFoundException('Beneficiário não encontrado');

      const user = await this.prisma.users.findUnique({
        where: {
          id: dependentId,
        },
        select: {
          cpf: true,
        },
      });

      if (!user) throw new NotFoundException('Beneficiário não encontrado');

      return user.cpf;
    } catch (error) {
      this.#logger.error(error);
      if (error instanceof HttpException) throw error;
    }
  }

  /**
   * Retorna as informações de uma consulta de telemedicina agendada pelo
   * beneficiário utilizando o identificador da base do fornecedor.
   *
   * @param cpf CPF do beneficiário.
   * @param externalId Identificador da consulta na base do fornecedor.
   * @returns Consulta de telemedicina agendada pelo beneficiário.
   *
   * @throws {NotFoundException} Se o usuário ou a consulta não forem encontrados.
   */
  getAppointmentByExternalId(cpf: string, externalId: string) {
    return from(
      this.prisma.users.findUnique({
        where: {
          cpf: cpf,
          telemedUsers: { telemedAppointments: { some: { externalId } } },
        },
        select: { telemedUsers: { select: { telemedAppointments: true } } },
      }),
    ).pipe(
      map((user) => {
        if (!user || !user.telemedUsers) {
          throw new NotFoundException('Usuário não encontrado');
        }

        return user.telemedUsers?.telemedAppointments.find(
          (appointment) => appointment.externalId === externalId,
        );
      }),
      map((appointment) => {
        if (!appointment) {
          throw new NotFoundException(`Consulta ${externalId} não encontrada.`);
        }
        // verifica se a consulta já foi cancelada
        if (['CANCELED', 'CANCELLED'].includes(appointment.statusName)) {
          throw new BadRequestException(
            `Consulta '${externalId}' já cancelada!`,
          );
        }

        return appointment;
      }),
      catchError((err) => {
        this.#logger.error(err);
        return throwError(() => err);
      }),
    );
  }

  /**
   * Vincula uma consulta de telemedicina agendada pelo beneficiário utilizando
   * o identificador da base do fornecedor.
   *
   * @param cpf CPF do beneficiário.
   * @param externalId Identificador da consulta na base do fornecedor.
   *
   * @todo converter metodo para upsert
   *  - motivo: o usuário pode já ter a consulta cancelada e reagendar a mesma
   */
  // TODO: converter metodo para upsert / unificar método updateAppointment
  linkAppointment(cpf: string, externalId: string, appointmentDate: Date) {
    if (!externalId)
      throw new BadRequestException('O externalId é obrigatório.');

    return from(
      this.prisma.users.update({
        where: { cpf },
        data: {
          telemedUsers: {
            update: {
              telemedAppointments: {
                create: {
                  externalId,
                  appointmentDate,
                  statusName: 'SCHEDULED',
                },
              },
            },
          },
        },
      }),
    ).pipe(
      catchError((err) => {
        this.#logger.error(err);
        throw err;
      }),
    );
  }

  /**
   * Atualiza o status da consulta de telemedicina agendada pelo beneficiário na
   * base Aliviaê. Importante a informação estar conforme a base do fornecedor.
   *
   * @param externalId Identificador da consulta na base do fornecedor.
   * @param cpf CPF do beneficiário.
   * @param status Novo status da consulta.
   *
   * @returns Consulta atualizada
   *
   * @todo unificar metodo com o linkAppointment
   */

  async updateAppointment(externalId: string, cpf: string, status: Status) {
    try {
      const user = await this.prisma.users.findUnique({
        where: { cpf },
        select: { telemedUsers: { select: { id: true } } },
      });

      if (!user || !user.telemedUsers) {
        throw new NotFoundException('Usuário não encontrado');
      }

      const telemedUserId = user!.telemedUsers!.id;

      const appointment = await this.prisma.telemedAppointments.findUnique({
        where: {
          externalId_telemedUserId: {
            telemedUserId,
            externalId,
          },
        },
      });

      if (!appointment) {
        throw new NotFoundException(`Consulta ${externalId} não encontrada.`);
      }

      const updatedAppointment = await this.prisma.telemedAppointments.update({
        data: { statusName: status },
        where: {
          externalId_telemedUserId: {
            telemedUserId,
            externalId,
          },
        },
      });

      return updatedAppointment;
    } catch (err) {
      this.#logger.error(err);
      throw err;
    }
  }
}

export default TelemedService;
