import JwtAuthGuard from '@/auth/jwt/jwt-auth.guard';
import {
  ExecutionContext,
  ForbiddenException,
  Injectable,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { JwtService } from '@nestjs/jwt';
import { Request } from 'express';
import TopmedController from './topmed.controller';

@Injectable()
class TopmedGuard extends JwtAuthGuard {
  constructor(
    private readonly config: ConfigService,
    protected readonly jwt: JwtService,
  ) {
    super(jwt);
  }

  /**
   * Verifica se o IP da requisição está na lista de IPs permitidos.
   */
  isAllowedOrigin(req: Request): boolean {
    const allowedIps = this.config.getOrThrow(
      'integrations.topmed.allowed-ips',
    );
    const ips = req.ips;

    if (Array.isArray(ips)) return ips.some((ip) => allowedIps.includes(ip));

    return allowedIps.includes(ips);
  }

  /**
   * Verifica se o handler da requisição é o de login.
   */
  private isLoginHandler(handler: string): boolean {
    const publicHandlers: (keyof TopmedController)[] = ['login'];
    return publicHandlers.includes(handler as keyof TopmedController);
  }

  /**
   * Verifica se o usuário é um usuário do Topmed.
   */
  private isTopmedUser(user: any): boolean {
    return !!user && user.supplier === 'topmed';
  }

  /**
   * Avalia as permissões de acesso aos endpoints do Topmed. Regras
   * específicas de endpoints estão definidas internamente no método.
   */
  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest<Request>();
    const handler = context.getHandler().name;

    if (!this.isAllowedOrigin(request))
      throw new ForbiddenException('Origem não autorizada');

    if (!this.isLoginHandler(handler)) {
      const isLogged = await super.canActivate(context);
      if (!isLogged) return false;

      return this.isTopmedUser(request.user);
    }

    return true;
  }
}
export default TopmedGuard;
