import {
  Call<PERSON><PERSON><PERSON>,
  ExecutionContext,
  Injectable,
  NestInterceptor,
} from '@nestjs/common';

import { map, Observable } from 'rxjs';

import type TelemedController from '../telemed.controller';

import GetPhysicianDto from './dto/get-physician.dto';
import GetAppointmentQueueDto from './dto/get-appointment-queue.dto';
import CancelAppointmentQueueDto from './dto/cancel-appointment-queue.dto';
import GetPhysicianSpecialityDto from './dto/get-physician-speciality.dto';
import GetBeneficiaryDto from '@/integrations/telemed/topmed/dto/get-beneficiary.dto';

@Injectable()
class TopmedInterceptor implements NestInterceptor {
  private formatListSpecialities(
    response: SpecialitiesResponse[],
  ): GetPhysicianSpecialityDto {
    const data = response.map((speciality) => {
      return {
        code: speciality.Codigo,
        description: speciality.Descricao,
        availableAt: speciality.DataProximaDisponibilidae,
      };
    });

    return { data };
  }

  private formatAvailablePhysicians(
    response: PhysiciansResponse[],
  ): GetPhysicianDto {
    const data = response.map((physician) => {
      const { HorariosComHash, Idiomas } = physician;
      const availability = HorariosComHash?.map((d) => ({
        id: d.Hash,
        date: d.Horario,
      }));
      const languages = Idiomas?.map((l) => ({ id: l.Codigo, name: l.Nome }));

      return {
        name: physician.Nome,
        speciality: physician.Especialidade,
        imageUrl: physician.CaminhoImagemCompleto,
        history: physician.HistoricoProfissional,
        registration: {
          acronym: physician.Sigla || 'CRM',
          number: physician.RegistroConselhoClasseProfissional,
        },
        languages: languages || null,
        availability: availability || null,
      };
    });

    return { data };
  }

  private formatJoinAppointmentQueue(
    response: JoinQueueResponse,
  ): GetAppointmentQueueDto {
    const data = {
      id: response.HashRequisicao,
      message: response.Mensagem,
      email: response.EmailDirecionado,
      url: response.LinkDirecionamento,
    };

    return { data };
  }

  private formatCancelAppointment(response: string): CancelAppointmentQueueDto {
    const data = { message: response };

    return { data };
  }

  private formatGetBeneficiary(response: PersonResponse): GetBeneficiaryDto {
    return {
      id: response.Id,
      name: response.Nome,
      cpf: response.Cpf,
      identifier: response.IdentificacaoPessoa,
    };
  }

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const method = context.getHandler().name;

    // formata a response de cada endpoint individualmente
    let formatter = (res: any) => res;
    switch (method as keyof TelemedController) {
      case 'listSpecialities':
        formatter = this.formatListSpecialities;
        break;

      case 'getAvailablePhysicians':
        formatter = this.formatAvailablePhysicians;
        break;

      case 'getBeneficiary':
        formatter = this.formatGetBeneficiary;
        break;

      case 'cancelAppointment':
        formatter = this.formatCancelAppointment;
        break;

      case 'joinAppointmentQueue':
        formatter = this.formatJoinAppointmentQueue;
        break;
    }

    // retorna a response formatada
    return next.handle().pipe(map((response) => formatter(response)));
  }
}

export default TopmedInterceptor;
