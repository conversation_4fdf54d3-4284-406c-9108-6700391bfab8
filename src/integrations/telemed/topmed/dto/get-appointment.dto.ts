import { ApiProperty } from '@nestjs/swagger';

class AppointmentPhysicianDto {
  @ApiProperty({ description: 'Nome do médico' })
  name: string;

  @ApiProperty({ description: 'Especialidade do médico' })
  speciality: string;
}

class AppointmentStatusDto {
  @ApiProperty({ description: 'ID da situação na TopMed' })
  id: number;

  @ApiProperty({ description: 'Nome da situação' })
  name: string;
}

export class AppointmentDto {
  @ApiProperty({ description: 'ID da consulta' })
  id: string;

  @ApiProperty({ description: 'Nome do usuário' })
  name: string;

  @ApiProperty({ description: 'CPF do usuário' })
  cpf: string;

  @ApiProperty({ description: 'ID do usuário' })
  userId: number;

  @ApiProperty({ description: 'URL da imagem de perfil do usuário' })
  imageUrl: string;

  @ApiProperty({ description: 'Sintomas informados para a consulta' })
  symptoms: string;

  @ApiProperty({ description: 'Informações do médico responsável' })
  physician: AppointmentPhysicianDto;

  @ApiProperty({ description: 'Situação da consulta' })
  status: AppointmentStatusDto;

  @ApiProperty({ description: 'Data e hora da consulta' })
  date: Date;
}

class GetAppointmentDto {
  @ApiProperty({
    description: 'Lista de consultas realizadas e agendadas',
    type: [AppointmentDto],
  })
  data: AppointmentDto[];
}

export default GetAppointmentDto;
