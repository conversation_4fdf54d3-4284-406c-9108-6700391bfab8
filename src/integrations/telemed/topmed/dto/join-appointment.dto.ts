import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

import {
  IsBoolean,
  IsNotEmpty,
  IsOptional,
  IsString,
  Length,
} from 'class-validator';

class JoinAppointmentDto {
  @IsNotEmpty()
  @IsString()
  @ApiProperty({ description: 'Sintomas que serão informados ao médico' })
  symptoms: string;

  @IsBoolean()
  @ApiProperty({ description: 'Ignorar chats já existentes', default: true })
  ignoreExistent: boolean = true;

  @IsOptional()
  @IsString()
  @IsNotEmpty()
  @Length(11)
  @ApiPropertyOptional({
    description: 'CPF do dependente',
    default: undefined,
    required: false,
  })
  dependentCpf?: string;
}

export default JoinAppointmentDto;
