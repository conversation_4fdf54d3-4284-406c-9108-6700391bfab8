import { ApiProperty } from '@nestjs/swagger';

import {
  IsEmail,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  MaxLength,
} from 'class-validator';
import { IsCPF } from 'class-validator-cpf';

class CreateTelemedDependentDto {
  @ApiProperty({ description: 'Nome completo' })
  @IsNotEmpty()
  @IsString()
  name: string;

  @IsNotEmpty()
  @IsNumber()
  @ApiProperty({ description: 'ID do dependente' })
  id: number;

  @ApiProperty({ description: 'CPF do dependente' })
  @IsNotEmpty()
  @IsCPF({ message: 'CPF inválido' })
  cpf: string;

  @ApiProperty({ description: 'Data de nascimento' })
  @IsNotEmpty()
  birthDate: Date | null;

  @ApiProperty({ description: 'Sexo' })
  @IsNotEmpty()
  gender: 'F' | 'M' | string;

  @ApiProperty({ description: 'Telefone de contato', nullable: true })
  @MaxLength(11)
  phone: string | null;

  @ApiProperty({ description: 'E-mail de contato', nullable: true })
  @IsEmail()
  email: string | null;

  @IsOptional()
  @ApiProperty({ description: 'Data de cadastro na topmed', nullable: true })
  registeredAt: string | null;
}

export default CreateTelemedDependentDto;
