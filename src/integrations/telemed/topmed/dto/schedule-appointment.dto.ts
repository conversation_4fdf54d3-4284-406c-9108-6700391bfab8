import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString, IsUUID } from 'class-validator';

class ScheduleAppointmentDto {
  @IsNotEmpty()
  @IsString()
  @ApiProperty({ description: 'Sintomas que serão informados para o médico' })
  symptoms: string;

  @IsNotEmpty()
  @IsUUID()
  @ApiProperty({
    description:
      'ID da data de consulta, vinda do endpoint de listagem de médicos',
  })
  appointmentId: string;
}

export default ScheduleAppointmentDto;
