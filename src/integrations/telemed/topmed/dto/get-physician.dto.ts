import { ApiProperty } from '@nestjs/swagger';

class PhysicianLanguagesDto {
  @ApiProperty({ description: 'Código do idioma na base TopMed' })
  id: string;

  @ApiProperty({ description: 'Idioma falado' })
  name: string;
}

class PhysicianAvailabilityDto {
  @ApiProperty({ description: 'ID da data de disponibilidade' })
  id: string;

  @ApiProperty({ description: 'Data da disponibilidade' })
  date: string;
}

class PhysicianRegistrationDto {
  @ApiProperty({ description: 'Número do registro' })
  number: string;

  @ApiProperty({ description: 'Sigla do registro', default: 'CRM' })
  acronym: 'CRM' | string;
}

/**
 * DTO retornado pela aplicação após formatação da response vinda da API TopMed.
 */
class PhysicianDto {
  @ApiProperty({ description: 'Nome do médico' })
  name: string;

  @ApiProperty({ description: 'Especialidade do médico' })
  speciality: string;

  @ApiProperty({ description: 'URL da imagem do médico' })
  imageUrl: string;

  @ApiProperty({ description: 'Informações de registro no CRM' })
  registration: PhysicianRegistrationDto;

  @ApiProperty({ description: 'Histórico de atendimento', nullable: true })
  history: string | null;

  @ApiProperty({
    description: 'Horários disponíveis para agendamento',
    nullable: true,
    type: [PhysicianAvailabilityDto],
  })
  availability: PhysicianAvailabilityDto[] | null;

  @ApiProperty({
    description: 'Idiomas disponíveis para agendamento',
    nullable: true,
    type: [PhysicianLanguagesDto],
  })
  languages: PhysicianLanguagesDto[] | null;
}

class GetPhysicianDto {
  @ApiProperty({
    description: 'Lista de médicos disponíveis',
    type: [PhysicianDto],
  })
  data: PhysicianDto[];
}

export default GetPhysicianDto;
