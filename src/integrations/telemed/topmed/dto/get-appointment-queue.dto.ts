import { ApiProperty } from '@nestjs/swagger';

import { IsUrl, IsUUID } from 'class-validator';

class AppointmentQueueDto {
  @IsUUID()
  @ApiProperty({ description: 'Identificador do chat de atendimento' })
  id: string;

  @IsUrl()
  @ApiProperty({ description: 'URL do chat de atendimento' })
  url: string;
}

class GetAppointmentQueueDto {
  @ApiProperty({ description: 'Informações do chat de autoatendimento' })
  data: AppointmentQueueDto;
}

export default GetAppointmentQueueDto;
