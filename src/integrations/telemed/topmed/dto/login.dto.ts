import { IsString, validate } from 'class-validator';
import { from } from 'rxjs';

class TopmedLoginDto {
  @IsString()
  username: string;

  @IsString()
  password: string;

  @IsString()
  grant_type: 'password' | string = 'password';

  constructor(args: { username: string; password: string }) {
    this.username = args.username;
    this.password = args.password;

    from(validate(this)).subscribe();
  }
}

export default TopmedLoginDto;
