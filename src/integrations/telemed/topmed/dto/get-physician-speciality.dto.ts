import { ApiProperty } from '@nestjs/swagger';

class PhysicianSpecialityDto {
  @ApiProperty({ description: 'Código da especialidade' })
  code: number;

  @ApiProperty({ description: 'Descrição da especialidade' })
  description: string;

  @ApiProperty({
    description: 'Data da disponibilidade mais próxima',
    nullable: true,
  })
  availableAt: string | null;
}

class GetPhysicianSpecialityDto {
  @ApiProperty({
    description: 'Lista de especialidades disponíveis',
    type: [PhysicianSpecialityDto],
  })
  data: PhysicianSpecialityDto[];
}

export default GetPhysicianSpecialityDto;
