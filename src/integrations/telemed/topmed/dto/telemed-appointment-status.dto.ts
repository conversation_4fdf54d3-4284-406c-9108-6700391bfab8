import { ApiProperty } from '@nestjs/swagger';
import { IsISO8601, IsNotEmpty, IsString } from 'class-validator';

export class TelemedAppointmentStatusDto {
  @ApiProperty({ description: 'CPF do beneficiário' })
  @IsString()
  @IsNotEmpty()
  IdentificacaoPessoa: string;

  @ApiProperty({ description: 'Chave retornada no processo de agendamento' })
  @IsString()
  @IsNotEmpty()
  HashAgendamento: string;

  @ApiProperty({ description: 'Data e hora que a cosulta foi iniciada' })
  @IsISO8601()
  @IsNotEmpty()
  DataInicioConsulta: string;

  @ApiProperty({ description: 'Data e hora que a cosulta foi finalizada' })
  @IsISO8601()
  @IsNotEmpty()
  DataUltimaAlteracao: string;

  @ApiProperty({
    description:
      'Justificativa utilizada pelo profissional para finalizar o atendimento',
  })
  @IsString()
  @IsNotEmpty()
  JustificativaAtendimento: string;
}
