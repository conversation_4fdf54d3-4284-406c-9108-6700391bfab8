import { ApiProperty } from '@nestjs/swagger';

import { IsNumber, IsString, Length } from 'class-validator';

class GetBeneficiaryDto {
  @IsNumber()
  @ApiProperty({ description: 'ID do beneficiário na base TopMed' })
  id: number;

  @IsString()
  @ApiProperty({ description: 'Nome do beneficiário' })
  name: string;

  @IsString()
  @Length(11)
  @ApiProperty({ description: 'CPF do beneficiário' })
  cpf: string;

  @IsString()
  @ApiProperty({ description: 'Identificação do beneficiário' })
  identifier: string;
}

export default GetBeneficiaryDto;
