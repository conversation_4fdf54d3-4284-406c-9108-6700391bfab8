import { Module } from '@nestjs/common';
import { HttpModule } from '@nestjs/axios';
import { ConfigModule, ConfigService } from '@nestjs/config';

import TopmedService from './topmed.service';

import NewRelicModule from '@/utils/new-relic/new-relic.module';
import NewRelicLogger from '@/utils/new-relic/new-relic.logger';
import TopmedController from './topmed.controller';
import AuthModule from '@/auth/auth.module';
import TelemedService from '../telemed.service';
import NotificationsModule from '@/notifications/notifications.module';

const TopmedHttpModule = HttpModule.registerAsync({
  imports: [ConfigModule],
  inject: [ConfigService],
  useFactory: async (config: ConfigService) => ({
    baseURL: config.get('integrations.topmed.url'),
  }),
});

@Module({
  imports: [TopmedHttpModule, NewRelicModule, AuthModule, NotificationsModule],
  providers: [TelemedService, TopmedService, ConfigService, NewRelicLogger],
  controllers: [TopmedController],
  exports: [TopmedService, TopmedHttpModule, NewRelicModule],
})
export default class TopmedModule {}
