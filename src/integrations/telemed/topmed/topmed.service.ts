import { stringify } from 'qs';

import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@nestjs/config';
import {
  BadRequestException,
  HttpException,
  Injectable,
  Logger,
  ServiceUnavailableException,
} from '@nestjs/common';

import {
  catchError,
  firstValueFrom,
  from,
  map,
  Observable,
  of,
  switchMap,
  throwError,
} from 'rxjs';

import PrismaService from '@/utils/prisma/prisma.service';
import NewRelicLogger from '@/utils/new-relic/new-relic.logger';

import CreateTelemedDependentDto from './dto/create-dependent.dto';
import { TelemedAppointmentStatusDto } from './dto/telemed-appointment-status.dto';
import { Status } from '@prisma/client';

@Injectable()
class TopmedService {
  readonly #logger = new Logger(TopmedService.name);
  protected authCredentials = { token: '', expiresIn: 0 };

  /** Código da J17 Bank na base da TopMed, utilizado como `CodigoEmpresa` nas requisições. */
  readonly #companyCode: string;

  /** ID da J17 Bank na base da TopMed, utilizado como `IdEmpresa` nas requisições. */
  readonly #companyId: string;

  constructor(
    private readonly config: ConfigService,
    private readonly http: HttpService,
    private readonly prisma: PrismaService,
    private readonly newRelic: NewRelicLogger,
  ) {
    this.#companyCode = this.config.getOrThrow('integrations.topmed.code');
    this.#companyId = this.config.getOrThrow('integrations.topmed.id');

    this.http.axiosRef.defaults.baseURL = this.config.get(
      'integrations.topmed.url',
    );

    // quantidade máxima de tentativas de comunicação com a API TopMed
    const ATTEMPTS = { total: 0, max: 5 };

    // FIXME: identificar o erro de forma mais detalhada
    const ApiTopmedError = (err: any) => {
      const error = { service: 'TOPMED', error: err.response?.data || err };
      this.#logger.fatal(error);
      this.newRelic.log('fatal', error as any);

      // verifica se existe erro no envio de informações
      if (error.error.length) {
        throw new BadRequestException(error.error);
      }

      if (err.response?.data) {
        if (typeof err.response.data === 'string') {
          err.response.data = JSON.parse(err.response.data);
        }

        const [message] = err.response.data.messages || [];
        throw new BadRequestException(message?.mensagem);
      }

      ATTEMPTS.total = 0;
      throw new ServiceUnavailableException(
        'Houve um problema ao se comunicar com a API TopMed',
      );
    };

    // interceptors para tratamento de responses
    this.http.axiosRef.interceptors.response.use(
      (response) => response,
      async (error) => {
        // incrementa o contador de tentativas
        ATTEMPTS.total++;

        // direciona os erros disparados dentro do interceptor de response
        if (error instanceof HttpException) {
          return Promise.reject(error);
        }

        // Retorna erro ao tentar cadastrar o usuário continuamente
        if (error.config.url.includes('CadastroPessoa')) {
          return ApiTopmedError(error);
        }

        // verifica se a quantidade máxima de tentativas foi atingida
        if (error.response?.status === 429 || ATTEMPTS.total >= ATTEMPTS.max) {
          ATTEMPTS.total = 0;
          this.#logger.debug('Quantidade máxima de tentativas atingida');
          return Promise.reject(error);
        }

        if (!error.response && !error.status) {
          return ApiTopmedError(error);
        }

        const statusCode = error.status;

        // FIXME: migrar regras de tratamento de erros
        if (statusCode === 400) {
          if ('RequestErrors' in error.response.data) {
            const { RequestErrors } = error.response.data;
            return ApiTopmedError(RequestErrors);
          }

          if (typeof error.response.data === 'string') {
            error.response.data = JSON.parse(error.response.data);
          }

          if (error.response.data.Result) {
            return Promise.reject(error.response.data.Result);
          }

          const [message] = error.response.data.messages || [];
          if (!message) {
            return ApiTopmedError(error);
          }

          return ApiTopmedError(error);
        }

        // caso o retorno de uma requisição for 401 ou 403, refaz a autenticação
        if (statusCode === 401 || statusCode === 403) {
          // refaz a autenticação quando o token expira
          await firstValueFrom(this.auth());

          // refaz a requisição
          return Promise.resolve(this.http.axiosRef(error.config));
        }

        return ApiTopmedError(error);
      },
    );

    // interceptors para tratamento de requisições
    this.http.axiosRef.interceptors.request.use((req) => {
      req.headers['Accept'] = '*/*';
      req.headers['Content-Type'] = 'application/json';

      delete req.headers['User-Agent'];

      // informa as requisições que estão sendo realizadas
      this.#logger.debug(`[${req.method!.toUpperCase()}]: ${req.url}`);

      // adiciona o cabeçalho x-www-form-urlencoded quando o corpo é uma string
      if (typeof req.data === 'string' && !req.data.includes('CPF')) {
        req.headers['Content-Type'] = 'application/x-www-form-urlencoded';
      }

      return req;
    });
  }

  /**
   * Cria um usuário Aliviaê na base de dados TopMed.
   *
   * @param cpf CPF do beneficiário que será a ponte entre as bases de dados.
   */
  registerCurrentBeneficiary(
    cpf: string,
  ): Observable<RegisterBeneficiaryResponse> {
    return from(
      this.prisma.users.findUnique({
        where: { cpf },
        select: {
          name: true,
          cpf: true,
          phone: true,
          email: true,
          birthDate: true,
          gender: true,
          asDependent: true,
        },
      }),
    ).pipe(
      switchMap((user) => {
        if (!user) {
          return throwError(
            () => new BadRequestException('Usuário não encontrado'),
          );
        }

        this.#logger.log(`Cadastrando beneficiário: ${user.name}`);

        const userFmt = {
          name: user.name,
          cpf: user.cpf,
          email: user.email,
          birthDate: user.birthDate,
          phone: user.phone,
          gender: user.gender === 'Masculino' ? 'M' : 'F',
        };

        const holderId = user.asDependent[0]?.holderId;

        // se for dependente, traz o CPF do titular, senão, traz undefined
        const holderCpf$ = holderId
          ? from(
              this.prisma.users.findFirst({
                where: { id: holderId },
                select: { cpf: true },
              }),
            ).pipe(map((holder) => holder?.cpf))
          : of(undefined);

        return holderCpf$.pipe(
          switchMap((holderCpf) =>
            // converte o corpo da requisição para o formato TopMed
            from(this.convertPersonToProvider(userFmt, holderCpf)),
          ),
          switchMap((userData) =>
            this.auth().pipe(
              // registra ou atualiza o usuário na TopMed
              switchMap(() => from(this.upsertBeneficiary(userData))),
            ),
          ),
        );
      }),
    );
  }

  /**
   * Realiza o login na API TopMed e armazena o token para futuras requisições
   * até sua expiração.
   */
  auth() {
    this.#logger.debug('Realizando autenticação...');

    const { username, password } = this.config.get('integrations.topmed');
    const body: AuthRequest = { username, password, grant_type: 'password' };

    return this.http.post<AuthResponse>('/v1/api/token', stringify(body)).pipe(
      map(({ data, status }) => {
        this.#logger.log(`Login realizado com o status ${status}`);

        // salva o token para futuras verificações
        this.authCredentials.token = data.access_token;
        this.authCredentials.expiresIn = data.expires_in;

        this.#logger.debug('Token armazenado!');

        this.http.axiosRef.defaults.headers.common = {
          ...this.http.axiosRef.defaults.headers.common,
          Authorization: `Bearer ${data.access_token}`,
        };
      }),
    );
  }

  /**
   * Busca as informações dos usuários para utilização nas próximas requisições.
   *
   * @param cpf CPF do beneficiário, previamente cadastrado na TopMed.
   * @return Informações do beneficiário na base da TopMed.
   */
  getPerson(cpf: string) {
    const body: PersonRequest = { CPF: cpf, IdEmpresa: this.#companyId };

    this.#logger.debug({ name: 'getPerson', body });

    // realiza o login antes de fazer a busca, para garantir a autenticação
    return this.auth().pipe(
      switchMap(() => {
        // busca pela pessoa a partir do seu CPF e retorna os dados
        return this.http.post<PersonResponse>('/api/pessoa/buscar', body);
      }),
      map(({ data }) => ({ ...data, Id: Math.floor(data.Id) })),
    );
  }

  /**
   * Busca pelo link do autoatendimento realizado pelo beneficiário,
   *
   * @param personId Identificador do beneficiário na base da TopMed.
   * @return O link do chat onde o autoatendimento foi realizado.
   */
  getHealthSelfServiceLink(personId: number) {
    return this.http
      .get<SelfServiceLinkResponse>(`/api/autoatendimento/link/${personId}`, {
        headers: { 'Content-Type': 'application/json' },
      })
      .pipe(map(({ data }) => data));
  }

  /**
   * Realiza uma solicitação para a API de forma a colocar o beneficiário na fila de espera
   * para atendimento em tempo real.
   *
   * @param cpf CPF do beneficiário.
   * @param symptoms Texto escrito pelo beneficiário, contendo sintomas e histórico de doenças.
   * @return Informações sobre a fila de atendimento ingressada pelo beneficiário.
   */
  joinAppointmentQueue(cpf: string, symptoms: string) {
    const body: JoinQueueRequest = {
      IdentificacaoPessoa: cpf,
      CodigoEmpresa: this.#companyCode,
      Sintomas: symptoms,
    };

    return this.http
      .post<JoinQueueResponse>('/api/filatendimento/entrarnafila', body)
      .pipe(map(({ data }) => data));
  }

  /**
   * Lista as especialidades dos médicos que estão disponíveis para consulta de acordo
   * com o plano do beneficiário solicitante.
   *
   * @param cpf CPF do beneficiário.
   * @returns Lista de especialidades disponíveis para escolha do médico.
   */
  listSpecialities(cpf: string) {
    const body: SpecialitiesRequest = {
      CodigoEmpresa: this.#companyCode,
      IdentificacaoPessoa: cpf,
    };

    return this.http
      .post<
        SpecialitiesResponse[]
      >('/api/especialidade/listarespecialidades', stringify(body))
      .pipe(
        map(({ data }) =>
          data.filter((item) => item.DataProximaDisponibilidae !== null),
        ),
      );
  }

  /**
   * Lista os médicos que atendem a uma especialidade de acordo com o plano do beneficiario.
   *
   * @param cpf CPF do beneficiário.
   * @param specialityId Identificador da especialidade dos médicos nas base da TopMed.
   * @param appointmentDate Data da consulta solicitada pelo beneficiário.
   * @returns Lista de médicos disponíveis para consulta.
   */
  listPhysicians(cpf: string, specialityId: string, appointmentDate: Date) {
    const body: PhysiciansRequest = {
      IdentificacaoPessoa: cpf,
      IdEspecialidade: specialityId,
      CodigoEmpresa: this.#companyCode,
      // formato: YYYY-MM-DDTHH:MM:SS
      DataConsulta: appointmentDate.toISOString().slice(0, 19),
    };

    this.#logger.debug({ name: 'listPhysicians', body });

    return this.http
      .post<
        PhysiciansResponse[]
      >('/api/agendaprofissional/medicos', stringify(body))
      .pipe(map(({ data }) => data));
  }

  /**
   * Agenda uma consulta médica para o beneficiário na TopMed.
   *
   * @param cpf CPF do beneficiário.
   * @param appointmentHash Hash da data disponível do médico, retornado no endpoint de listagem de médicos.
   * @param symptoms Texto escrito pelo beneficiário, contendo sintomas e histórico de doenças.
   * @return Informação da consulta agendada, no caso de sucesso.
   */
  scheduleAppointment(cpf: string, appointmentHash: string, symptoms: string) {
    const body: ScheduleAppointmentRequest = {
      IdentificacaoPessoa: cpf,
      CodigoEmpresa: this.#companyCode,
      Hash: appointmentHash,
      Sintomas: symptoms,
    };

    this.#logger.debug({ name: 'scheduleAppointment', body });

    return this.http
      .post<ScheduleAppointmentResponse>(
        '/api/agendamento/agendarconsulta',
        stringify(body),
      )
      .pipe(map(({ data }) => data));
  }

  /**
   * Lista as consultas anteriores realizadas pelo beneficiário.
   *
   * @param cpf CPF do beneficiário.
   * @return Lista com as informações das consultas.
   */
  listAppointments(cpf: string) {
    const body: ListAppointmentsRequest = {
      IdentificacaoPessoa: cpf,
      CodigoEmpresa: this.#companyCode,
    };

    this.#logger.debug({ name: 'listAppointments', body });

    return this.http
      .get<ListAppointmentsResponse[]>('/api/agendamento/consultasanteriores', {
        data: stringify(body),
      })
      .pipe(map(({ data }) => data));
  }

  /**
   * Cancela uma consulta agendada anteriormente pelo beneficiário.
   *
   * @param hash Hash da consulta agendada e que será cancelada.
   * @return Mensagem estática de sucesso ou falha da requisição.
   */
  cancelAppointment(hash: string) {
    const body: CancelAppointmentRequest = { Hash: hash };

    this.#logger.debug({ name: 'cancelAppointment', body });

    return this.http
      .post<CancelAppointmentResponse>(
        '/api/agendamento/cancelarconsulta',
        stringify(body),
      )
      .pipe(map(({ data }) => data));
  }

  /**
   * Cadastra ou atualiza o beneficiário na base da TopMed.
   *
   * @todo Realizar o vínculo entre IDs na base Aliviaê.
   * @param person Informações do beneficiário que serão enviadas para a TopMed.
   * @return Informações do cadastro ou atualização do beneficiário enviado.
   */
  upsertBeneficiary(person: Omit<RegisterBeneficiaryRequest, 'CodigoEmpresa'>) {
    const body = { ...person, CodigoEmpresa: this.#companyCode };

    this.#logger.debug({ name: 'upsertBeneficiary', body });

    return this.http
      .post<RegisterBeneficiaryResponse>('/v2/api/CadastroPessoa', body)
      .pipe(map(({ data }) => data));
  }

  /**
   * Registra um beneficiário na base da TopMed identificando-o como um dependente.
   *
   * @param holderId ID do beneficário titular.
   * @param dependent Informações do dependente que serão enviadas para a TopMed.
   *
   * @throws BadRequestException se o ID do titular for diferente do ID do token.
   */
  registerDependent(
    holderId: number,
    dependent: Omit<RegisterBeneficiaryRequest, 'CodigoEmpresa'>,
  ) {
    const holder$ = from(
      this.prisma.users.findUnique({ where: { id: holderId } }),
    );

    return holder$.pipe(
      switchMap((holder) => {
        if (!holder) throw new BadRequestException('Usuário não encontrado');

        // atribui o CPF do titular para realizar o vínculo na TopMed
        dependent.BeneficiarioTitular = holder.cpf;

        return this.auth().pipe(
          switchMap(() => {
            return this.upsertBeneficiary(dependent);
          }),
        );
      }),
      catchError((err) => {
        this.#logger.error(err);
        throw err;
      }),
    );
  }

  /**
   * Verifica se as credenciais fornecidas correspondem às credenciais de webhook
   * armazenadas na config.
   *
   * @param credentials - Objeto contendo o nome de usuário e senha do webhook.
   * @returns Retorna `true` se as credenciais forem válidas, caso contrário `false`.
   *
   * @throws Lança uma exceção se as credenciais de configuração não forem encontradas.
   */
  checkWebhookCredentials(credentials: {
    username: string;
    password: string;
  }): boolean {
    const webhookUsername = this.config.getOrThrow<string>(
      'integrations.topmed.wehbook-username',
    );
    const webhookPassword = this.config.getOrThrow<string>(
      'integrations.topmed.webhook-password',
    );

    return (
      credentials.username === webhookUsername &&
      credentials.password === webhookPassword
    );
  }

  async convertPersonToProvider(
    person: Omit<CreateTelemedDependentDto, 'id' | 'registeredAt'>,
    holderCpf: string = '',
  ): Promise<Omit<RegisterBeneficiaryRequest, 'CodigoEmpresa'>> {
    return {
      IdentificacaoPessoa: person.cpf,
      BairroResidencial: '',
      BeneficiarioTitular: holderCpf,
      CEPResidencial: '',
      CPFCNPJ: person.cpf,
      CodigoCarteira: person.cpf,
      CodigoSubempresa: '2',
      DataNascimento:
        person.birthDate instanceof Date
          ? person.birthDate.toISOString().split('T')[0]
          : person.birthDate || '',
      Email: person.email || '',
      // TODO: Migrar dados do banco para UF antes de realizar o cadastro
      EstadoResidencial: '',
      LogradouroResidencial: '',
      MunicipioResidencial: '',
      Nome: person.name || '',
      PaisResidencial: '',
      ListaProdutos: [
        { CodigoProduto: '49', Situacao: '1' },
        { CodigoProduto: '89', Situacao: '1' },
      ],
      Sexo: person.gender as 'M' | 'F',
      StatusBeneficiario: '1',
      TelefoneCelular: person.phone || '',
      TelefoneComercial: person.phone || '',
      TelefoneResidencial: person.phone || '',
      TipoPessoa: holderCpf.length ? '2' : '1',
    };
  }

  async convertPersonToApp(
    person: Omit<RegisterBeneficiaryRequest, 'CodigoEmpresa'>,
  ): Promise<CreateTelemedDependentDto> {
    const user = await this.prisma.users.findUnique({
      where: { cpf: person.IdentificacaoPessoa },
      include: {
        userAddress: {
          where: { homeInsurance: true },
          take: 1,
          include: {
            address: {
              include: {
                city: {
                  select: {
                    name: true,
                    state: true,
                  },
                },
              },
            },
          },
        },
      },
    });

    if (!user) throw new BadRequestException('Usuário nao encontrado');

    return <CreateTelemedDependentDto>{
      birthDate: new Date(person.DataNascimento),
      cpf: person.IdentificacaoPessoa,
      email: person.Email || user.email || '',
      gender: person.Sexo,
      name: person.Nome,
      phone: person.TelefoneCelular || user.phone || '',
    };
  }

  /**
   * Converte o status da consulta da TopMed para o formato de Status da API.
   *
   * @param status Status da consulta da TopMed.
   * @returns Status da consulta no formato da API do APP.
   *
   * @throws {BadRequestException} Se o status da TopMed for desconhecido.
   */
  convertAppointmentStatusToApp(
    status: TelemedAppointmentStatusDto,
  ): AppointmentStatus {
    const statusMap: Record<string, Status> = {
      Agendado: 'SCHEDULED',
      'Não Realizado': 'CANCELLED',
      Cancelado: 'CANCELLED',
      Concluído: 'COMPLETED',
      'Teleconsulta Concluída': 'COMPLETED',
      'Problemas técnicos': 'CANCELLED',
      'No show': 'CANCELLED',
    };

    const mappedStatus = statusMap[status.JustificativaAtendimento];

    if (!mappedStatus) {
      throw new BadRequestException(
        `Status desconhecido: ${status.JustificativaAtendimento}`,
      );
    }

    return {
      externalId: status.HashAgendamento,
      cpf: status.IdentificacaoPessoa,
      status: mappedStatus,
    };
  }
}

export default TopmedService;
