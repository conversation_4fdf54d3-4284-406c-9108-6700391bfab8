//TODO: Remover o CANCELED
type Status = 'SCHEDULED' | 'CANCELED' | 'CANCELLED' | 'COMPLETED' | 'PENDING';

// /v1/api/token
type AuthResponse = {
  access_token: string;
  token_type: 'Bearer' | string;
  expires_in: number;
};
type AuthRequest = {
  username: string;
  password: string;
  grant_type: 'password' | string;
};

// /api/pessoa/buscar
type PersonResponse = {
  Id: number;
  Nome: string;
  Cpf: string;
  IdentificacaoPessoa: string;
};
type PersonRequest = {
  CPF: string;
  IdEmpresa: string;
};

// /api/autoatendimento/link/{personId}
type SelfServiceLinkResponse = {
  Id: string;
  UrlChat: string;
};

// /api/filatendimento/entrarnafila
type JoinQueueResponse = {
  HashRequisicao: string;
  LinkDirecionamento: string;
  EmailDirecionado?: string;
  Mensagem: string;
};
type JoinQueueRequest = {
  CodigoEmpresa: string;
  IdentificacaoPessoa: string;
  Sintomas: string;
};

// /api/especialidades/listarespecialidades
type SpecialitiesResponse = {
  Codigo: number;
  Descricao: string;
  DataProximaDisponibilidae: string | null;
};
type SpecialitiesRequest = {
  CodigoEmpresa: string;
  IdentificacaoPessoa: string;
};

// /api/agendaprofissional/medicos
type PhysiciansResponse = {
  Nome: string;
  Especialidade: string;
  Sigla: string;
  RegistroConselhoClasseProfissional: string;
  NumeroRegistroConselho: string;
  CaminhoImagemCompleto: string;
  HistoricoProfissional: string | null;
  HorariosComHash: { Horario: string; Hash: string }[] | null;
  Idiomas: { Codigo: string; Nome: string }[];
};
type PhysiciansRequest = {
  IdentificacaoPessoa: string;
  CodigoEmpresa: string;
  DataConsulta: string; // AAAA-MM-DDT00:00:00
  IdEspecialidade: string;
};

// /api/agendamento/agendarconsulta
type ScheduleAppointmentResponse = {
  DataDaConsulta: string;
  Sintomas: string;
  Situacao: number;
  NomeDaSituacao: string;
  NomeDoMedico: string;
  Hash: string;
  Nome: string;
  Especialidade: string;
  Sigla: string;
  RegistroConselhoClasseProfissional: string;
  NumeroRegistroConselho: string; //`CRM - ${RegistroConselhoClasseProfissional}`;
  CaminhoImagem: string;
  CaminhoImagemCompleto: string;
  UrlTeleConsulta: string;
};
type ScheduleAppointmentRequest = {
  IdentificacaoPessoa: string;
  CodigoEmpresa: string;
  Hash: string;
  Sintomas: string;
};

// /api/agendamento/consultasanteriores
type ListAppointmentsResponse = {
  DataDaConsulta: string;
  Sintomas: string;
  Situacao: number;
  NomeDaSituacao: string;
  NomeDoMedico: string;
  Hash: string;
  Nome: string;
  Especialidade: string;
  CaminhoImagemCompleto: string;
};
type ListAppointmentsRequest = {
  IdentificacaoPessoa: string;
  CodigoEmpresa: string;
};

// /api/agendamento/cancelarconsulta
type CancelAppointmentResponse = string;
type CancelAppointmentRequest = {
  Hash: string;
};

// /api/pessoa/cadastrarbeneficiario
type RegisterBeneficiaryResponse = {
  Hash: string;
  Result: string;
  ProcessedDate: string;
  RequestErrors: string[];
};
type RegisterBeneficiaryRequest = {
  // Identificador na base TopMed, devendo ser único para cada pessoa
  IdentificacaoPessoa: string;
  // Define o código a ser utilizado para login no app TopMed
  CodigoCarteira: string;
  // Nome do beneficiário
  Nome: string;
  // CPF do beneficiário
  CPFCNPJ: string;
  // Data de nascimento do beneficiário
  DataNascimento: string;
  Sexo: 'F' | 'M';
  // 1 = Titular, 2 = Dependente
  TipoPessoa: '1' | '2';
  // 1 = Ativo, 2 = Inativo
  StatusBeneficiario: '1' | '2';
  CodigoEmpresa: string;
  CodigoSubempresa: string;
  SituacaoProduto?: '1' | '0';
  Produtos?: string;
  ListaProdutos?: { CodigoProduto: string; Situacao: '1' | '0' }[];
  Email?: string;
  LogradouroResidencial?: string;
  CEPResidencial?: string;
  BairroResidencial?: string;
  MunicipioResidencial?: string;
  EstadoResidencial?: string;
  PaisResidencial?: string;
  TelefoneComercial?: string;
  TelefoneCelular?: string;
  TelefoneResidencial?: string;
  // deixar vazio no caso do titular e preencher com o cpf do titular no caso de dependente
  BeneficiarioTitular?: string;
};

// webhook
type AppointmentStatus = {
  externalId: string;
  cpf: string;
  status: Status;
};
