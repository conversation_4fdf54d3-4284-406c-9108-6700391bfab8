import { of } from 'rxjs';
import { map } from 'rxjs/operators';

describe('TopmedService', () => {
  describe('listSpecialities', () => {
    let serviceMock: any;
    const mockUser = { cpf: '12345678901' };

    beforeEach(() => {
      // Mock do serviço com as especialidades
      serviceMock = {
        listSpecialities: jest.fn().mockImplementation(() =>
          of([
            {
              Codigo: 9.0,
              Descricao: 'Cirurgia Cardiovascular',
              DataProximaDisponibilidae: null,
            },
            {
              Codigo: 61.0,
              Descricao: 'Enfermeiro',
              DataProximaDisponibilidae: null,
            },
            {
              Codigo: 53.0,
              Descricao: 'Médico da Família',
              DataProximaDisponibilidae: '2025-01-11T12:00:00',
            },
            {
              Codigo: 66.0,
              Descricao: 'Psicologia',
              DataProximaDisponibilidae: '2025-01-11T12:00:00',
            },
          ]).pipe(
            map((data) =>
              data.filter((item) => item.DataProximaDisponibilidae !== null),
            ),
          ),
        ),
      };
    });

    it('should filter specialities with non-null DataProximaDisponibilidae', async () => {
      const { firstValueFrom } = await import('rxjs');

      // Use o CPF vindo da variável mockada
      const result = await firstValueFrom(
        serviceMock.listSpecialities(mockUser.cpf),
      );

      expect(result).toEqual([
        {
          Codigo: 53.0,
          Descricao: 'Médico da Família',
          DataProximaDisponibilidae: '2025-01-11T12:00:00',
        },
        {
          Codigo: 66.0,
          Descricao: 'Psicologia',
          DataProximaDisponibilidae: '2025-01-11T12:00:00',
        },
      ]);

      // Verificação de que o método foi chamado com o CPF
      expect(serviceMock.listSpecialities).toHaveBeenCalledWith(mockUser.cpf);
    });
  });
});
