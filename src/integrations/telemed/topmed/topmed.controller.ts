import {
  Controller,
  Post,
  Body,
  UnauthorizedException,
  UseGuards,
  Logger,
} from '@nestjs/common';
import { ApiBody, ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import SupplierLoginDto from './dto/supplier-login.dto';
import TopmedService from './topmed.service';
import TopmedGuard from './topmed.guard';
import AuthService from '@/auth/auth.service';
import { TelemedAppointmentStatusDto } from './dto/telemed-appointment-status.dto';
import TelemedService from '../telemed.service';
import FirebaseService from '@/notifications/firebase/firebase.service';

@ApiTags('integrations/topmed')
@UseGuards(TopmedGuard)
@Controller({ path: 'integrations/topmed', version: '1' })
class TopmedController {
  readonly #logger = new Logger(TopmedController.name);
  constructor(
    private readonly auth: AuthService,
    private readonly telemed: TelemedService,
    private readonly topmed: TopmedService,
    private readonly firebase: FirebaseService,
  ) {}

  @ApiOperation({ summary: 'Realiza o login do usuário' })
  @ApiBody({
    type: SupplierLoginDto,
    description: 'Credenciais de acesso',
  })
  @ApiResponse({
    status: 201,
    description: 'Login realizado com sucesso',
    example: { access_token: 'token' },
  })
  @ApiResponse({
    status: 401,
    description: 'Credenciais inválidas',
  })
  @ApiResponse({
    status: 403,
    description: 'Origem não autorizada',
  })
  @ApiOperation({ summary: 'Realiza login' })
  @Post('/login')
  async login(@Body() body: SupplierLoginDto) {
    const isValid = this.topmed.checkWebhookCredentials(body);

    if (!isValid) throw new UnauthorizedException('Credenciais inválidas');

    const authToken = await this.auth.generateToken({
      supplier: 'topmed',
      id: 0,
      cpf: '',
      email: '',
    });

    return { access_token: authToken.token, expires_in: authToken.expiresIn };
  }

  @Post('/webhook')
  @ApiOperation({ summary: 'Recebe status da teleconsulta' })
  @ApiResponse({
    status: 201,
    description: 'Status processado com sucesso',
    schema: {
      example: {
        HashAgendamento: 'abc123',
        MensagemConfirmacao: 'Consulta processada com sucesso',
      },
    },
  })
  @ApiResponse({
    status: 400,
    description: 'Consulta ou usuário não encontrados',
  })
  @ApiResponse({
    status: 404,
    description: 'Status da consulta não encontrado',
  })
  async Webhook(@Body() body: TelemedAppointmentStatusDto) {
    try {
      // converte status da telemedicina para status interno
      const { externalId, cpf, status } =
        this.topmed.convertAppointmentStatusToApp(body);

      // atualiza o status da consulta
      await this.telemed.updateAppointment(externalId, cpf, status);

      // TODO: implementar armazenamento de fcm token
      // TODO: buscar token do usuário
      const token = 'TOKEN_DO_USUARIO';

      // envia notificação push para usuário (fire and forget)
      this.firebase.sendAppointmentNotification(status, token).subscribe();

      // retorna confirmação
      return {
        HashAgendamento: externalId,
        MensagemConfirmacao: 'Consulta processada com sucesso',
      };
    } catch (error) {
      this.#logger.error(`Erro ao processar webhook: ${error.message}`);
      throw error;
    }
  }
}

export default TopmedController;
