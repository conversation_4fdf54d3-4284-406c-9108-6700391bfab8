import UserService from '@/user/user.service';
import { Injectable, NotFoundException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { from, of, switchMap } from 'rxjs';

@Injectable()
class TelevetsService {
  readonly #url: string;
  readonly #customer: string;
  readonly #plan: string;

  constructor(
    private readonly user: UserService,
    private readonly config: ConfigService,
  ) {
    this.#customer = this.config.getOrThrow('integrations.televets.customer');
    this.#plan = this.config.getOrThrow('integrations.televets.plan');
    this.#url = this.config.getOrThrow('integrations.televets.iframeUrl');
  }
  /**
   * Retorna a URL de um iframe da TeleVet, incluindo todas as informações
   * necessárias para a consulta.
   *
   * @param cpf CPF do beneficiário.
   * @return URL do iframe.
   */
  getIframeUrl(cpf: string) {
    return from(this.user.findByCpf(cpf)).pipe(
      switchMap((user) => {
        if (!user) throw new NotFoundException('Usuário não encontrado');

        const payload = {
          customer: this.#customer,
          plano: this.#plan,
          tutorName: user.name,
          petName: '',
          cpf: user.cpf,
          phone: user.phone,
          mail: user.email,
        };

        const payloadJson = JSON.stringify(payload);
        // converte o payload para base64
        const payloadBase64 = Buffer.from(payloadJson, 'utf-8').toString(
          'base64',
        );

        const fullUrl = `${this.#url}${payloadBase64}`;
        return of({ url: fullUrl });
      }),
    );
  }
}

export default TelevetsService;
