import { Test, TestingModule } from '@nestjs/testing';
import TelevetsService from '../televets.service';
import UserService from '@/user/user.service';
import { NotFoundException } from '@nestjs/common';
import { firstValueFrom } from 'rxjs';
import { ConfigService } from '@nestjs/config';

describe('TelevetsService', () => {
  describe('getIframeUrl', () => {
    let service: TelevetsService;
    let userService: UserService;

    const mockCustomer = 'J17 Bank';
    const mockPlan = 'Aliviaê';
    const mockIframeUrl =
      'https://televetsbrasil.com.br/b2b-tlv-comm-j17bank/?customer-data=';

    beforeEach(async () => {
      const module: TestingModule = await Test.createTestingModule({
        providers: [
          TelevetsService,
          {
            provide: UserService,
            useValue: {
              findByCpf: jest.fn(),
            },
          },
          {
            provide: ConfigService,
            useValue: {
              getOrThrow: jest.fn((key: string) => {
                const values: { [key: string]: string } = {
                  'integrations.televets.customer': mockCustomer,
                  'integrations.televets.plan': mockPlan,
                  'integrations.televets.iframeUrl': mockIframeUrl,
                } as const;
                return values[key];
              }),
            },
          },
        ],
      }).compile();

      service = module.get<TelevetsService>(TelevetsService);
      userService = module.get<UserService>(UserService);
    });

    it('deve retornar a URL com base nos dados do usuário', async () => {
      const mockUser = {
        name: 'João',
        cpf: '12345678900',
        phone: '11999999999',
        email: '<EMAIL>',
      };

      (userService.findByCpf as jest.Mock).mockResolvedValue(mockUser);

      const result = await firstValueFrom(service.getIframeUrl(mockUser.cpf));

      expect(result.url).toContain(mockIframeUrl);
      expect(result.url).toContain(
        Buffer.from(
          JSON.stringify({
            customer: mockCustomer,
            plano: mockPlan,
            tutorName: mockUser.name,
            petName: '',
            cpf: mockUser.cpf,
            phone: mockUser.phone,
            mail: mockUser.email,
          }),
          'utf-8',
        ).toString('base64'),
      );
    });

    it('deve lançar NotFoundException se o usuário não for encontrado', async () => {
      (userService.findByCpf as jest.Mock).mockResolvedValue(null);

      await expect(
        firstValueFrom(service.getIframeUrl('00000000000')),
      ).rejects.toThrow(NotFoundException);
    });
  });
});
