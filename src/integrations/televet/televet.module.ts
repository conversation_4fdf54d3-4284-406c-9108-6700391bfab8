import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { JwtModule } from '@nestjs/jwt';

import TelevetsService from './televets/televets.service';
import TelevetController from './televet.controller';
import AuthModule from '@/auth/auth.module';
import UserModule from '@/user/user.module';

const JWTModule = JwtModule.registerAsync({
  imports: [ConfigModule],
  inject: [ConfigService],
  useFactory: async (config: ConfigService) => ({
    signOptions: { expiresIn: config.get<number>('app.jwt.expiration')! * 60 },
    secret: config.get<string>('app.jwt.secret'),
  }),
});

@Module({
  imports: [AuthModule, UserModule, ConfigModule, JWTModule],
  controllers: [TelevetController],
  providers: [TelevetsService],
  exports: [TelevetsService],
})
export default class TelevetModule {}
