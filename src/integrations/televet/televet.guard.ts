import { ExecutionContext, Injectable } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';

import JwtAuthGuard from '@/auth/jwt/jwt-auth.guard';
import UserService from '@/user/user.service';

import type { Request } from 'express';

@Injectable()
class TelevetGuard extends JwtAuthGuard {
  constructor(
    protected readonly jwt: JwtService,
    private readonly user: UserService,
  ) {
    super(jwt);
  }

  /**
   * Avalia as permissões de acesso aos endpoints de Telemedicina. Regras
   * específicas de endpoints estão definidas internamente no método.
   *
   * Regras globais:
   *  - **DEVE** possuir vínculo com o benefício "Telemedicina Pet"
   */
  async canActivate(context: ExecutionContext): Promise<boolean> {
    const isLogged = await super.canActivate(context);
    if (!isLogged) return false;

    // busca pelo usuário logado
    const { user } = context.switchToHttp().getRequest<Request>();
    if (!user || user.supplier === 'topmed') return false;

    // verifica se o usuário possui o benefício da "Telemedicina Pet"
    const benefits = await this.user.getUserBenefits(user.id);

    const hasTelevet = benefits?.some(({ name }) =>
      name!.includes('Telemedicina Pet'),
    );
    if (!hasTelevet) return false;

    return true;
  }
}

export default TelevetGuard;
