import {
  ApiBearerAuth,
  <PERSON>piOperation,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { Controller, Get, Request, UseGuards } from '@nestjs/common';
import TelevetsService from './televets/televets.service';
import TelevetGuard from './televet.guard';
import { catchError } from 'rxjs';

@ApiBearerAuth()
@ApiTags('integrations/televets')
@ApiResponse({
  status: 403,
  description: 'Usuário não possui acesso ao benefício',
})
@UseGuards(TelevetGuard)
@Controller({ path: 'integrations/televet', version: '1' })
class TelevetController {
  constructor(private readonly televets: TelevetsService) {}

  @ApiResponse({
    status: 200,
    description: 'Url do iframe retornada com sucesso',
    example: {
      url: 'https://example.com/iframe',
    },
  })
  @ApiResponse({
    status: 404,
    description: 'Usuário não encontrado',
  })
  @ApiOperation({
    summary: 'Busca URL do iframe de consultas da Televets',
  })
  @Get('iframe-url')
  getIframeUrl(@Request() req: Express.Request) {
    const { cpf } = req.user!;

    return this.televets.getIframeUrl(cpf).pipe(
      catchError((err) => {
        throw err;
      }),
    );
  }
}

export default TelevetController;
