import {
  BadRequestException,
  Body,
  Controller,
  Delete,
  Get,
  HttpException,
  HttpStatus,
  Logger,
  NotFoundException,
  Param,
  ParseIntPipe,
  Post,
  Put,
  Request,
  Res,
  UseGuards,
  UseInterceptors,
  ValidationPipe,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiBody,
  ApiOperation,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';

import { catchError, firstValueFrom, from, of } from 'rxjs';

import UserService from './user.service';

import { GetBenefitsDto } from '@/benefits/dto/get-benefits.dto';
import TelemedService from '@/integrations/telemed/telemed.service';
import TopmedService from '@/integrations/telemed/topmed/topmed.service';
import EmailService from '@/notifications/email/email.service';
import { Response } from 'express';
import ChangePasswordDto from './dto/change-password.dto';
import CreateDependentDto from './dto/create-dependent.dto';
import CreateUserDto from './dto/create-user.dto';
import GetUserAddressDto from './dto/get-user-address.dto';
import GetUserDto from './dto/get-user.dto';
import UpdateDependentDto from './dto/update-dependent.dto';
import UpdateUserDto from './dto/update-user.dto';
import UpsertBankSoftUserDto from './dto/upsert-banksoft-user.dto';
import UserGuard from './user.guard';
import UserTransformInterceptor from './user.interceptor';

@ApiTags('users')
@UseGuards(UserGuard)
@ApiBearerAuth()
@Controller({ path: 'users', version: '1' })
class UserController {
  readonly #logger = new Logger(UserController.name);

  constructor(
    private readonly user: UserService,
    private readonly telemed: TelemedService,
    private readonly topmed: TopmedService,
    private readonly emailService: EmailService,
  ) {}

  @ApiOperation({ summary: 'Cria um novo usuário titular' })
  @ApiResponse({
    status: 201,
    type: Number,
    description: 'ID do usuário criado',
  })
  @ApiResponse({
    status: 400,
    description: 'Dados inválidos ou usuário já cadastrado',
  })
  @ApiResponse({
    status: 404,
    description: 'Plano ou empresa não encontrados',
  })
  @ApiBody({
    type: CreateUserDto,
    description: 'Dados do usuário a ser criado',
  })
  @Post()
  async create(@Body(ValidationPipe) data: CreateUserDto) {
    try {
      // Verifica se o usuário já existe
      const existingUser = await this.user.findByCpf(data.cpf);
      if (existingUser) {
        throw new BadRequestException('Usuário já cadastrado com este CPF');
      }

      // Valida companyId foram fornecidos
      if (!data.companyId) {
        data.companyId = await this.user.getDefaultCompanyId();
      }

      // Valida planId foram fornecidos
      if (!data.planId) {
        data.planId = await this.user.getDefaultPlanId(data.companyId);
      }

      const userId = await this.user.createUser(data);

      // enviar e-mail de boas-vindas
      await this.emailService.sendWelcome({
        name: data.name,
        email: data.email,
        appName: 'Aliviae',
        appUrl: 'https://app.aliviae.com',
      });

      return userId;
    } catch (err) {
      if (err instanceof HttpException) throw err;
      throw new BadRequestException('Erro ao criar usuário');
    }
  }

  @ApiOperation({ summary: 'Atualiza os dados de um usuário usando seu CPF' })
  @ApiResponse({
    status: 200,
    type: Number,
    description: 'ID do usuário',
  })
  @ApiResponse({
    status: 404,
    description: 'Usuário ou cidade não encontrados',
  })
  @ApiBody({ type: UpdateUserDto, description: 'Dados que serão atualizados' })
  @Put('/:cpf')
  async update(
    @Param('cpf') cpf: string,
    @Body(ValidationPipe) data: UpdateUserDto,
  ) {
    try {
      const user = await this.user.findByCpf(cpf);

      if (!user) throw new NotFoundException('Usuário nao encontrado!');

      return await this.user.update(user.id, data);
    } catch (err) {
      this.#logger.fatal('Erro ao criar o usuário', err);
      if (err instanceof HttpException) throw err;
      throw new Error('Erro ao atualizar usuário');
    }
  }

  @ApiResponse({ status: 400, description: 'Usuário não encontrado' })
  @ApiOperation({ summary: 'Busca um usuário pelo seu ID' })
  @ApiResponse({ status: 200, type: GetUserDto })
  @Get('/:id')
  @UseInterceptors(UserTransformInterceptor)
  findOne(@Param('id', ParseIntPipe) id: number) {
    return from(this.user.findById(id));
  }

  @ApiOperation({ summary: 'Busca um usuário pelo seu CPF' })
  @ApiResponse({ status: 200, type: GetUserDto })
  @Get('/cpf/:cpf')
  async findByCpf(
    @Res({ passthrough: true }) res: Response,
    @Param('cpf') cpf: string,
  ) {
    const user = await this.user.findByCpf(cpf);
    if (!user) {
      res.status(HttpStatus.NOT_FOUND);
      return { message: 'User not existent' };
    }

    res.status(HttpStatus.OK);
    return { message: 'User existent' };
  }

  @ApiOperation({ summary: 'Lista os benefícios de um usuário' })
  @ApiResponse({ status: 400, description: 'Benefícios não encontrados' })
  @ApiResponse({ status: 200, type: GetBenefitsDto })
  @Get('/:id/benefits')
  getUserBenefits(@Param('id', ParseIntPipe) id: number) {
    return from(this.user.getUserBenefits(id));
  }

  @ApiOperation({ summary: 'Lista os dependentes de um usuário titular' })
  @ApiResponse({
    status: 200,
    type: [GetUserAddressDto],
    description: 'Lista de dependentes do usuário titular',
  })
  @ApiResponse({ status: 400, description: 'Usuário não encontrado' })
  @Get('/:holderId/dependents')
  @UseInterceptors(UserTransformInterceptor)
  listUserDependents(@Param('holderId', ParseIntPipe) holderId: number) {
    return this.user.getUserDependents(holderId);
  }

  @ApiOperation({ summary: 'Cria um usuário dependente' })
  @ApiResponse({
    status: 201,
    type: Number,
    description: 'ID do usuário dependente',
  })
  @ApiResponse({
    status: 404,
    description: 'Titular ou plano não encontrados',
  })
  @ApiResponse({
    status: 409,
    description: 'Usuário já cadastrado ou limite de dependentes atingido',
  })
  @Post('/:holderId/dependents')
  createUserDependent(
    @Param('holderId', ParseIntPipe) holderId: number,
    @Body(ValidationPipe) data: CreateDependentDto,
  ) {
    return from(this.user.createDependent(holderId, data));
  }

  @ApiOperation({ summary: 'Edita um dependente de um usuário titular' })
  @ApiResponse({
    status: 200,
    type: Number,
    description: 'ID do usuário dependente',
  })
  @ApiResponse({
    status: 404,
    description: 'Usuário não encontrado',
  })
  @Put('/:holderId/dependents/:dependentId')
  updateUserDependent(
    @Param('holderId', ParseIntPipe) holderId: number,
    @Param('dependentId', ParseIntPipe) dependentId: number,
    @Body(ValidationPipe) data: UpdateDependentDto,
  ) {
    return from(this.user.updateDependent(holderId, dependentId, data));
  }

  @ApiOperation({ summary: 'Remove um dependente de um usuário titular' })
  @ApiResponse({
    status: 200,
    type: Number,
    description: 'Usuário removido com sucesso',
  })
  @ApiResponse({
    status: 404,
    description: 'Usuário dependente/titular não encontrado',
  })
  @Delete('/:holderId/dependents/:dependentId')
  removeUserDependent(
    @Param('holderId', ParseIntPipe) holderId: number,
    @Param('dependentId', ParseIntPipe) dependentId: number,
  ) {
    return from(this.user.removeDependent(holderId, dependentId)).pipe(
      catchError((err) => {
        if (err instanceof HttpException) throw err;
        return of();
      }),
    );
  }

  @Post('/change-password')
  @ApiOperation({ summary: 'Redefine a senha do usuário' })
  @ApiResponse({
    status: 201,
    description: 'Senha redefinida com sucesso',
    example: { message: 'Senha alterada com sucesso!' },
  })
  @ApiResponse({
    status: 400,
    description: 'Senha atual incorreta ou senha e confirmação não coincidem',
  })
  @ApiResponse({ status: 404, description: 'Usuário não encontrado' })
  async changePassword(
    @Body() data: ChangePasswordDto,
    @Request() req: Express.Request,
  ) {
    const userId = req.user!.id;

    try {
      const user = await this.user.findById(userId);
      if (!user) throw new NotFoundException('Usuário não encontrado');

      const isValidPassword = await this.user.verifyPassword(data, userId);
      if (!isValidPassword)
        throw new BadRequestException('Senha atual incorreta');

      return await this.user.changePassword(userId, data.newPassword);
    } catch (error) {
      this.#logger.error('Erro ao alterar senha:', error);
      throw error;
    }
  }

  @Post('/bank-soft')
  @ApiOperation({
    summary: 'Cria ou atualiza um usuário titular com dados do BankSoft',
  })
  @ApiResponse({
    status: 201,
    description: 'Usuário criado ou atualizado com sucesso',
  })
  @ApiResponse({
    status: 400,
    description: 'Erro ao processar dados do BankSoft',
  })
  async upsertBankSoftUser(
    @Body(new ValidationPipe({ transform: true })) data: UpsertBankSoftUserDto,
  ) {
    try {
      // Converte os dados do BankSoft para o formato interno
      const userData = this.user.convertBankSoftToCreateUser(data);

      // Busca o plano e a empresa para clientes CCB (só usado no insert)
      const planAndCompany = await this.user.getBanksoftPlanAndCompany();

      // Faz upsert na tabela de usuários
      const user = await this.user.upsertBanksoftUser(
        userData,
        planAndCompany.planId,
        planAndCompany.companyId,
      );

      // Registra o beneficiário na base de dados TopMed
      const registeredUser = await firstValueFrom(
        this.topmed.registerCurrentBeneficiary(userData.cpf),
      );

      // Busca os dados do beneficiário na Topmed
      const telemedUser = await firstValueFrom(
        this.topmed.getPerson(userData.cpf),
      );

      // cria o beneficiário na base de dados
      await this.telemed.createTelemedUser({
        id: user.id,
        telemedId: telemedUser.Id.toString(),
        registeredAt: new Date(registeredUser.ProcessedDate),
      });

      return {
        id: user.id,
      };
    } catch (error) {
      this.#logger.error('Erro ao processar usuário BankSoft:', error);
      throw error;
    }
  }
}

export default UserController;
