import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { HttpModule } from '@nestjs/axios';

import KycModule from '@/kyc/kyc.module';
import KycService from '@/kyc/kyc.service';
import NotificationsModule from '@/notifications/notifications.module';
import EmailService from '@/notifications/email/email.service';
import EncryptModule from '@/utils/encrypt/encrypt.module';
import EncryptService from '@/utils/encrypt/encrypt.service';
import TelemedService from '@/integrations/telemed/telemed.service';
import TopmedService from '@/integrations/telemed/topmed/topmed.service';
import NewRelicModule from '@/utils/new-relic/new-relic.module';
import NewRelicLogger from '@/utils/new-relic/new-relic.logger';

import UserService from './user.service';
import UserController from './user.controller';
import { JwtModule } from '@nestjs/jwt';
import AddressService from '@/address/address.service';

const JWTModule = JwtModule.registerAsync({
  imports: [ConfigModule],
  inject: [ConfigService],
  useFactory: async (config: ConfigService) => ({
    signOptions: { expiresIn: config.get<number>('app.jwt.expiration')! * 60 },
    secret: config.get<string>('app.jwt.secret'),
  }),
});

@Module({
  imports: [
    EncryptModule,
    NotificationsModule,
    KycModule,
    HttpModule,
    NewRelicModule,
    JWTModule,
  ],
  providers: [
    UserService,
    AddressService,
    EncryptService,
    EmailService,
    KycService,
    ConfigService,
    TelemedService,
    TopmedService,
    NewRelicLogger,
  ],
  controllers: [UserController],
  exports: [
    UserService,
    AddressService,
    EncryptService,
    EmailService,
    KycService,
  ],
})
export default class UserModule {}
