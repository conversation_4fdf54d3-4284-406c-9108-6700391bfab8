import { ExecutionContext, Injectable } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';

import JwtAuthGuard from '@/auth/jwt/jwt-auth.guard';
import { Request } from 'express';

@Injectable()
class UserGuard extends JwtAuthGuard {
  constructor(protected readonly jwt: JwtService) {
    super(jwt);
  }

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const isLogged = await super.canActivate(context);
    if (!isLogged) return false;

    const { user } = context.switchToHttp().getRequest<Request>();
    if (!user || user.supplier === 'topmed') return false;

    return true;
  }
}

export default UserGuard;
