import { BadRequestException, NotFoundException } from '@nestjs/common';
import { Test, TestingModule } from '@nestjs/testing';

import TelemedService from '@/integrations/telemed/telemed.service';
import TopmedService from '@/integrations/telemed/topmed/topmed.service';
import CreateUserDto from '../dto/create-user.dto';
import UserController from '../user.controller';
import UserService from '../user.service';

describe('UserController', () => {
  let controller: UserController;
  let userService: UserService;
  let telemedService: TelemedService;
  let topmedService: TopmedService;

  const mockUserService = {
    findByCpf: jest.fn(),
    createUser: jest.fn(),
    findById: jest.fn(),
    getUserBenefits: jest.fn(),
    getUserDependents: jest.fn(),
    createDependent: jest.fn(),
    updateDependent: jest.fn(),
    removeDependent: jest.fn(),
    verifyPassword: jest.fn(),
    changePassword: jest.fn(),
    convertBankSoftToCreateUser: jest.fn(),
    getBanksoftPlanAndCompany: jest.fn(),
    upsertBanksoftUser: jest.fn(),
    update: jest.fn(),
  };

  const mockTelemedService = {
    createTelemedUser: jest.fn(),
  };

  const mockTopmedService = {
    registerCurrentBeneficiary: jest.fn(),
    getPerson: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [UserController],
      providers: [
        {
          provide: UserService,
          useValue: mockUserService,
        },
        {
          provide: TelemedService,
          useValue: mockTelemedService,
        },
        {
          provide: TopmedService,
          useValue: mockTopmedService,
        },
      ],
    }).compile();

    controller = module.get<UserController>(UserController);
    userService = module.get<UserService>(UserService);
    telemedService = module.get<TelemedService>(TelemedService);
    topmedService = module.get<TopmedService>(TopmedService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('create', () => {
    const validCreateUserDto: CreateUserDto = {
      name: 'João Silva',
      email: '<EMAIL>',
      cpf: '12345678901',
      birthDate: '1990-01-01',
      gender: 'M',
      phone: '11999999999',
      companyId: 1,
      planId: 1,
      street: 'Rua das Flores, 123',
      number: '123',
      zipCode: '01234567',
      city: 'São Paulo',
      state: 'SP',
      neighborhood: 'Centro',
      complement: 'Apto 101',
    };

    it('deve criar um novo usuário com sucesso', async () => {
      // Arrange
      const expectedUserId = { id: 1 };
      mockUserService.findByCpf.mockResolvedValue(null);
      mockUserService.createUser.mockResolvedValue(expectedUserId);

      // Act
      const result = await controller.create(validCreateUserDto);

      // Assert
      expect(result).toEqual(expectedUserId);
      expect(mockUserService.findByCpf).toHaveBeenCalledWith(
        validCreateUserDto.cpf,
      );
      expect(mockUserService.createUser).toHaveBeenCalledWith(
        validCreateUserDto,
      );
    });

    it('deve lançar BadRequestException quando usuário já existe', async () => {
      // Arrange
      const existingUser = { id: 1, cpf: '12345678901' };
      mockUserService.findByCpf.mockResolvedValue(existingUser);

      // Act & Assert
      await expect(controller.create(validCreateUserDto)).rejects.toThrow(
        new BadRequestException('Usuário já cadastrado com este CPF'),
      );
      expect(mockUserService.findByCpf).toHaveBeenCalledWith(
        validCreateUserDto.cpf,
      );
      expect(mockUserService.createUser).not.toHaveBeenCalled();
    });

    it('deve lançar BadRequestException quando planId está ausente', async () => {
      // Arrange
      const invalidDto = { ...validCreateUserDto, planId: undefined };
      mockUserService.findByCpf.mockResolvedValue(null);

      // Act & Assert
      await expect(
        controller.create(invalidDto as unknown as CreateUserDto),
      ).rejects.toThrow(new BadRequestException('ID do plano é obrigatório'));
      expect(mockUserService.createUser).not.toHaveBeenCalled();
    });

    it('deve lançar BadRequestException quando companyId está ausente', async () => {
      // Arrange
      const invalidDto = { ...validCreateUserDto, companyId: undefined };
      mockUserService.findByCpf.mockResolvedValue(null);

      // Act & Assert
      await expect(
        controller.create(invalidDto as unknown as CreateUserDto),
      ).rejects.toThrow(new BadRequestException('ID da empresa é obrigatório'));
      expect(mockUserService.createUser).not.toHaveBeenCalled();
    });

    it('deve lançar BadRequestException quando userService.createUser falha', async () => {
      // Arrange
      mockUserService.findByCpf.mockResolvedValue(null);
      mockUserService.createUser.mockRejectedValue(new Error('Database error'));

      // Act & Assert
      await expect(controller.create(validCreateUserDto)).rejects.toThrow(
        new BadRequestException('Erro ao criar usuário'),
      );
      expect(mockUserService.findByCpf).toHaveBeenCalledWith(
        validCreateUserDto.cpf,
      );
      expect(mockUserService.createUser).toHaveBeenCalledWith(
        validCreateUserDto,
      );
    });

    it('deve preservar HttpException quando lançada pelo service', async () => {
      // Arrange
      const httpException = new NotFoundException('Plano não encontrado');
      mockUserService.findByCpf.mockResolvedValue(null);
      mockUserService.createUser.mockRejectedValue(httpException);

      // Act & Assert
      await expect(controller.create(validCreateUserDto)).rejects.toThrow(
        httpException,
      );
      expect(mockUserService.findByCpf).toHaveBeenCalledWith(
        validCreateUserDto.cpf,
      );
      expect(mockUserService.createUser).toHaveBeenCalledWith(
        validCreateUserDto,
      );
    });
  });

  describe('instanciação do controller', () => {
    it('deve estar definido', () => {
      expect(controller).toBeDefined();
    });

    it('deve ter userService injetado', () => {
      expect(userService).toBeDefined();
    });

    it('deve ter telemedService injetado', () => {
      expect(telemedService).toBeDefined();
    });

    it('deve ter topmedService injetado', () => {
      expect(topmedService).toBeDefined();
    });
  });
});
