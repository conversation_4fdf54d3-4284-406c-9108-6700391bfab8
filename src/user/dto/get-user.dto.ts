import GetAddressDto from '@/address/dto/get-adress.dto';
import { ApiProperty } from '@nestjs/swagger';

class GetUserDto {
  @ApiProperty({ description: 'ID do usuário' })
  id: number;

  @ApiProperty({ description: 'ID do usuário titular' })
  holderId: number;

  @ApiProperty({ description: 'Nome completo do usuário' })
  name: string;

  @ApiProperty({ description: 'CPF do usuário' })
  cpf: string;

  @ApiProperty({ description: 'E-mail de contato' })
  email: string;

  @ApiProperty({ description: 'Telefone de contato', nullable: true })
  phone: string | null;

  @ApiProperty({ description: 'Data de nascimento do usuário', nullable: true })
  birthDate: Date | null;

  @ApiProperty({ description: 'Sexo do usuário', nullable: true })
  gender: string | null;

  @ApiProperty({ description: 'ID do processo anti-fraude', nullable: true })
  processId: string | null;

  @ApiProperty({ description: 'Score do usuário', nullable: true })
  score: number | null;

  @ApiProperty({ description: 'Senha do usuário', nullable: true })
  password: string | null;

  @ApiProperty({ description: 'ID do titular' })
  isHolder: boolean;

  @ApiProperty({
    description: 'Endereço do usuário',
    type: GetAddressDto,
    nullable: true,
  })
  address: GetAddressDto | null;
}

export default GetUserDto;
