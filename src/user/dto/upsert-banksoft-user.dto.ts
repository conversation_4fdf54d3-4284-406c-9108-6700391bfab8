import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import {
  IsEmail,
  IsNotEmpty,
  IsOptional,
  IsString,
  Length,
  MaxLength,
  MinLength,
} from 'class-validator';
import { IsCPF } from 'class-validator-cpf';

class CadastrarCliente {
  @ApiProperty({ description: 'Nome do cliente' })
  @IsNotEmpty()
  @IsString()
  @MinLength(3)
  @MaxLength(40)
  nome_cliente: string;

  @ApiProperty({
    description: 'CPF do cliente (aceita com ou sem formatação)',
  })
  @IsNotEmpty({ message: 'CPF é obrigatório' })
  @IsString({ message: 'CPF deve ser uma string' })
  @MinLength(11)
  @MaxLength(14)
  @IsCPF({ message: 'CPF inválido: verifique os dígitos' })
  cpfcnpj_cliente: string;

  @ApiProperty({ description: 'E-mail do cliente' })
  @IsNotEmpty()
  @IsString()
  @IsEmail()
  @MaxLength(60)
  email_cliente: string;

  @ApiProperty({ description: 'Telefone do cliente' })
  @IsNotEmpty()
  @IsString()
  @MaxLength(15)
  fonecel: string;

  @ApiProperty({ description: 'Data de nascimento do cliente' })
  @IsString()
  @IsNotEmpty()
  nascimento: string;

  @ApiProperty({ description: 'Sexo do cliente' })
  @IsNotEmpty()
  @IsString()
  sexo: string;

  @ApiProperty({ description: 'Bairro do cliente' })
  @IsNotEmpty()
  @IsString()
  @MaxLength(20)
  bairro_cliente: string;

  @ApiProperty({ description: 'CEP do cliente' })
  @IsNotEmpty()
  @IsString()
  @MaxLength(8)
  cep_cliente: string;

  @ApiProperty({ description: 'Cidade do cliente' })
  @IsNotEmpty()
  @IsString()
  @MaxLength(30)
  cidade_cliente: string;

  @ApiProperty({ description: 'Complemento do endereço' })
  @IsOptional()
  @IsString()
  @MaxLength(9)
  complemento: string | null;

  @ApiProperty({ description: 'Endereço do cliente' })
  @IsNotEmpty()
  @IsString()
  @MinLength(3)
  @MaxLength(40)
  endereco_cliente: string;

  @ApiProperty({ description: 'Número do endereço' })
  @IsString()
  @MaxLength(5)
  numendereco: string | null;

  @ApiProperty({ description: 'Estado do cliente' })
  @IsNotEmpty()
  @IsString()
  @Length(2)
  estado_cliente: string;
}

class UpsertBankSoftUserDto {
  @ApiProperty({ description: 'Dados do cliente' })
  @Transform(({ obj }) => obj.cadastrarCliente || obj.cadastrarcliente)
  cadastrarCliente: CadastrarCliente;
}

export default UpsertBankSoftUserDto;
