import { ApiProperty } from '@nestjs/swagger';

import { IsNotEmpty, IsStrongPassword } from 'class-validator';

class ChangePasswordDto {
  @IsNotEmpty()
  @ApiProperty({ description: 'Senha atual do usuário' })
  currentPassword: string;

  @IsStrongPassword()
  @IsNotEmpty()
  @ApiProperty({ description: 'Nova senha que será utilizada' })
  newPassword: string;

  @IsStrongPassword()
  @IsNotEmpty()
  @ApiProperty({ description: 'Confirmação da nova senha' })
  confirmPassword: string;
}
export default ChangePasswordDto;
