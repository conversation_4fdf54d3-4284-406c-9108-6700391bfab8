import {
  IsDateS<PERSON>,
  IsEmail,
  IsNotEmpty,
  IsN<PERSON>ber,
  IsO<PERSON>al,
  IsString,
  MaxLength,
  <PERSON><PERSON>eng<PERSON>,
} from 'class-validator';

import { ApiProperty } from '@nestjs/swagger';
import { IsCPF } from 'class-validator-cpf';

class CreateDependentDto {
  @ApiProperty({ description: 'E-mail do usuário' })
  @IsNotEmpty()
  @IsString()
  @IsEmail()
  @MaxLength(255)
  email?: string;

  @ApiProperty({ description: 'Nome completo' })
  @IsNotEmpty()
  @IsString()
  @MinLength(3)
  @MaxLength(255)
  name: string;

  @ApiProperty({ description: 'CPF' })
  @IsNotEmpty()
  @MaxLength(11)
  @MinLength(11)
  @IsCPF()
  cpf: string;

  @ApiProperty({ description: 'Data de nascimento' })
  @IsNotEmpty()
  @IsDateString()
  birthDate: string;

  @ApiProperty({ description: 'Gênero' })
  @IsNotEmpty()
  @IsString()
  @MinLength(1)
  @MaxLength(1)
  gender: string;

  @ApiProperty({ description: 'Telefone para contato' })
  @IsNotEmpty()
  @IsString()
  @MaxLength(11)
  phone: string;

  @ApiProperty({ description: 'ID da company' })
  @IsNumber()
  @IsOptional()
  companyId?: number;

  @ApiProperty({ description: 'ID do plano' })
  @IsNumber()
  @IsOptional()
  planId?: number;
}

export default CreateDependentDto;
