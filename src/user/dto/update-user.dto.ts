import { OmitType } from '@nestjs/mapped-types';
import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsDateString, IsOptional } from 'class-validator';

import CreateUserDto from './create-user.dto';

class UpdateUserDto extends OmitType(CreateUserDto, ['cpf'] as const) {
  @ApiPropertyOptional({ description: 'Data de renovação' })
  @IsOptional()
  @IsDateString()
  renovationDate?: string;
}

export default UpdateUserDto;
