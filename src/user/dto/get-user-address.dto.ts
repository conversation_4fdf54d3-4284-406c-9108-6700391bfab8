import GetAddressDto from '@/address/dto/get-adress.dto';
import { ApiProperty } from '@nestjs/swagger';

class GetUserAddressDto {
  @ApiProperty({ description: 'ID do usuário' })
  id: number;

  @ApiProperty({ description: 'Nome completo do usuário' })
  name: string;

  @ApiProperty({ description: 'Data do cadastro do usuário' })
  createdAt: string;

  @ApiProperty({ description: 'CPF do usuário' })
  cpf: string;

  @ApiProperty({ description: 'E-mail de contato' })
  email: string;

  @ApiProperty({ description: 'Data de nascimento' })
  birthDate: Date;

  @ApiProperty({ description: 'Sexo do usuário' })
  gender: string;

  @ApiProperty({ description: 'Telefone de contato', nullable: true })
  phone: string | null;

  @ApiProperty({ description: 'Identifica se o usuário é titular' })
  isHolder: boolean;

  @ApiProperty({
    description: 'Endereço do usuário',
    type: GetAddressDto,
    nullable: true,
  })
  address: GetAddressDto | null;
}

export default GetUserAddressDto;
