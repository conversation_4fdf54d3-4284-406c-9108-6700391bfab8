import {
  IsDateS<PERSON>,
  IsEmail,
  IsNotEmpty,
  IsNumber,
  IsO<PERSON>al,
  IsString,
  MaxLength,
  <PERSON><PERSON>ength,
} from 'class-validator';

import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

import { CreateAddressDto } from '@/address/dto/create-address.dto';
import { IsCPF } from 'class-validator-cpf';

class CreateUserDto extends CreateAddressDto {
  @ApiProperty({ description: 'E-mail do usuário' })
  @IsNotEmpty()
  @IsString()
  @IsEmail()
  @MaxLength(255)
  email: string;

  @ApiPropertyOptional({ description: 'Senha do usuário', minLength: 6 })
  @MinLength(6)
  @IsString()
  @IsOptional()
  @MaxLength(255)
  password?: string;

  @ApiProperty({ description: 'Nome completo' })
  @IsNotEmpty()
  @IsString()
  @MinLength(3)
  @MaxLength(255)
  name: string;

  @ApiProperty({ description: 'CPF' })
  @IsNotEmpty()
  @IsCPF()
  cpf: string;

  @ApiProperty({ description: 'Data de nascimento' })
  @IsNotEmpty()
  @IsDateString()
  birthDate: string;

  @ApiProperty({ description: 'Gênero' })
  @IsNotEmpty()
  @IsString()
  @MinLength(1)
  @MaxLength(1)
  gender: string;

  @ApiProperty({ description: 'Telefone para contato' })
  @IsNotEmpty()
  @IsString()
  @MaxLength(11)
  phone: string;

  @ApiProperty({ description: 'ID da company' })
  @IsNumber()
  @IsOptional()
  companyId?: number;

  @ApiProperty({ description: 'ID do plano' })
  @IsNumber()
  @IsOptional()
  planId?: number;
}

export default CreateUserDto;
