import {
  <PERSON><PERSON><PERSON><PERSON>,
  ExecutionContext,
  Injectable,
  NestInterceptor,
} from '@nestjs/common';
import { map } from 'rxjs';
import GetUserAddessDto from './dto/get-user-address.dto';

@Injectable()
export class UserTransformInterceptor implements NestInterceptor {
  intercept(context: ExecutionContext, next: CallHandler) {
    return next.handle().pipe(
      map((data: any | any[]) => {
        const transform = (user: any): GetUserAddessDto => ({
          id: user.id,
          name: user.name,
          cpf: user.cpf,
          email: user.email,
          birthDate: user.birthDate,
          gender: user.gender,
          phone: user.phone,
          createdAt: user.createdAt,
          address: user.userAddress?.[0]?.address
            ? {
                street: user.userAddress[0]?.address?.street,
                number: user.userAddress[0]?.address?.number,
                zipCode: user.userAddress[0]?.address?.zipCode,
                complement: user.userAddress[0]?.address?.complement,
                city: user.userAddress[0]?.address?.city?.name?.toLowerCase(),
                state: user.userAddress[0]?.address?.city?.stateAcronym,
                neighborhood: user.userAddress[0]?.address?.neighborhood,
              }
            : null,
          isHolder: user.isHolder,
        });

        return Array.isArray(data) ? data.map(transform) : transform(data);
      }),
    );
  }
}

export default UserTransformInterceptor;
