import {
  BadRequestException,
  ConflictException,
  HttpException,
  Injectable,
  Logger,
  NotFoundException,
} from '@nestjs/common';

import KYCService from '@/kyc/kyc.service';
import EmailService from '@/notifications/email/email.service';
import EncryptService from '@/utils/encrypt/encrypt.service';
import PrismaService from '@/utils/prisma/prisma.service';
import { Prisma } from '@prisma/client';

import AddressService from '@/address/address.service';
import { BenefitInterface } from '@/benefits/interfaces/benefit.interface';
import { TextFormatter } from '@/utils/text-formatter.util';
import ChangePasswordDto from './dto/change-password.dto';
import CreateDependentDto from './dto/create-dependent.dto';
import CreateUserDto from './dto/create-user.dto';
import UpdateDependentDto from './dto/update-dependent.dto';
import UpdateUserDto from './dto/update-user.dto';
import UpsertBankSoftUserDto from './dto/upsert-banksoft-user.dto';

@Injectable()
export default class UserService {
  readonly #logger = new Logger(UserService.name);
  public findUnique;

  constructor(
    private readonly prisma: PrismaService,
    private readonly address: AddressService,
    private readonly encrypt: EncryptService,
    private readonly email: EmailService,
    private readonly kyc: KYCService,
  ) {
    this.findUnique = this.prisma.users.findUnique;
  }

  /**
   * Calcula a data de renovação (data atual + 1 ano).
   *
   * @returns Data de renovação como Date.
   */
  private getRenovationDate(): Date {
    const currentDate = new Date();
    const renovationDate = new Date(currentDate);

    renovationDate.setFullYear(currentDate.getFullYear() + 1);

    return renovationDate;
  }

  /**
   * Cria um usuário (dependente ou titular).
   *
   * @param createUserDto Dados do usuário.
   * @param ctx Contexto de transação opcional.
   * @returns O ID do novo usuário.
   *
   * @throws {BadRequestException} Se o CPF ou outro campo único
   *                              for duplicado.
   */
  async create(
    createUserDto: CreateDependentDto | CreateUserDto,
    ctx: Prisma.TransactionClient = this.prisma,
  ): Promise<number> {
    const { ...user } = createUserDto;

    // seta renovation date dinamicamente para 1 ano a partir da criação do usuário
    const renovationDate = this.getRenovationDate().toISOString();

    try {
      const newUser = await ctx.users.create({
        data: {
          email: user.email?.toLowerCase(),
          name: TextFormatter.toTitleCase(user.name),
          cpf: TextFormatter.extractNumbers(user.cpf),
          birthDate: new Date(user.birthDate),
          gender: user.gender,
          phone: TextFormatter.extractNumbers(user.phone),
          plan: { connect: { id: user.planId } },
          renovationDate: new Date(renovationDate),
          companies: { connect: { id: user.companyId } },
        },
      });

      return newUser.id;
    } catch (error) {
      this.#logger.error('Erro ao criar usuário:', error);
      if (error.code === 'P2002') {
        const target = error.meta.target[0];
        throw new BadRequestException(`${target} já cadastrado`);
      }
      if (error instanceof HttpException) throw error;
      throw new BadRequestException('Erro ao criar usuário');
    }
  }

  /**
   * Cria um usuário dependente e o associa a um usuário titular existente.
   *
   * @param holderId ID do usuário titular.
   * @param data Dados do novo usuário dependente.
   * @returns ID do usuário dependente criado.
   */
  async createDependent(holderId: number, data: CreateDependentDto) {
    try {
      const user = await this.prisma.users.findUnique({
        where: { id: holderId },
      });

      if (!user) throw new NotFoundException('Usuário titular nao encontrado!');

      // busca dependentes ativos
      const dependentsCount = await this.prisma.userDependents.count({
        where: { holderId: holderId },
      });

      // verifica se titular já possui quantidade máxima de dependentes
      if (dependentsCount >= 3)
        throw new ConflictException('Limite de dependentes atingido');

      if (!user.planId)
        throw new NotFoundException('Plano do titular nao encontrado!');

      // atribui companyId e planId do titular ao dependente
      data.companyId = user.companyId;
      data.planId = user.planId;

      return this.prisma.$transaction(async (ctx) => {
        const dependentId = await this.create(data, ctx);
        await ctx.userDependents.create({ data: { holderId, dependentId } });

        return { id: dependentId };
      });
    } catch (error) {
      this.#logger.error(error);
      if (error instanceof HttpException) throw error;
      throw new BadRequestException('Erro ao criar dependente');
    }
  }

  /**
   * Atualiza as informações de um usuário.
   *
   * @param userId ID do usuário que será atualizado.
   * @param data Novas informações do usuário.
   * @returns As informações do usuário atualizado.
   */
  async update(userId: number, data: UpdateUserDto): Promise<{ id: number }> {
    const updateUser = await this.prisma.$transaction(async (ctx) => {
      // Atualiza o endereço
      await this.address.save(data, userId, ctx);

      // Atualiza os dados do usuário
      const userUpdated = await ctx.users.update({
        where: { id: userId },
        data: {
          email: data.email?.toLowerCase(),
          name: TextFormatter.toTitleCase(data.name),
          birthDate: new Date(data.birthDate),
          gender: data.gender,
          phone: TextFormatter.extractNumbers(data.phone),
          ...(data.renovationDate && {
            renovationDate: new Date(data.renovationDate),
          }),
        },
      });

      return userUpdated;
    });

    return { id: updateUser.id };
  }

  /**
   * Atualiza as informações de um usuário dependente.
   *
   * @param holderId ID do usuário titular.
   * @param dependentId ID do usuário dependente.
   * @param data Novas informações do usuário dependente.
   * @returns ID do usuário dependente atualizado.
   */
  async updateDependent(
    holderId: number,
    dependentId: number,
    data: UpdateDependentDto,
  ) {
    try {
      const dependent = await this.prisma.userDependents.findFirst({
        where: { holderId, dependentId },
      });

      if (!dependent) throw new NotFoundException('Usuário não encontrado');

      await this.prisma.users.update({
        where: { id: dependentId },
        data: {
          email: data.email?.toLowerCase(),
          name: TextFormatter.toTitleCase(data.name),
          birthDate: new Date(data.birthDate),
          gender: data.gender,
          phone: TextFormatter.extractNumbers(data.phone),
        },
      });

      return { id: dependentId };
    } catch (error) {
      this.#logger.error(error);
      if (error instanceof HttpException) throw error;

      throw new BadRequestException('Erro ao atualizar dependente');
    }
  }

  async findById(id: number) {
    try {
      const user = await this.prisma.users.findUnique({
        where: { id },
        select: {
          id: true,
          email: true,
          name: true,
          renovationDate: true,
          cpf: true,
          gender: true,
          birthDate: true,
          phone: true,
          asDependent: true,
          companyId: true,
          planId: true,
          userAddress: {
            include: {
              address: {
                include: {
                  city: true,
                },
              },
            },
          },
        },
      });

      if (!user) throw new BadRequestException('Usuário nao encontrado');

      //verifica se o usuário é titular
      const isHolder = user?.asDependent.length === 0;

      return { ...user, isHolder };
    } catch (error) {
      throw new BadRequestException(
        error.message || 'Erro ao encontrar usuário',
      );
    }
  }

  async findByCpf(cpf: string) {
    if (cpf.charCodeAt(0) === 0xfeff) {
      cpf = cpf.slice(1);
    }
    // TODO: fazer mapper para tratar retorno
    try {
      const user = await this.prisma.users.findUnique({
        where: { cpf: cpf },
        include: {
          asDependent: true,
        },
        omit: { password: true },
      });

      if (user) {
        const isHolder = user.asDependent.length === 0;
        return { ...user, isHolder };
      }

      return null;
    } catch (error) {
      throw new Error(`Error finding user with cpf ${cpf}: ${error.message}`);
    }
  }

  /**
   * Busca pelas informações de um usuário dependente.
   *
   * @param holderId ID do usuário titular.
   * @param dependentId ID do usuário dependente.
   * @returns As informações do usuário dependente.
   */
  async findUserDependent(holderId: number, dependentId: number) {
    const holder = await this.findById(holderId);
    if (!holder) throw new Error('Usuário titular não encontrado!');

    const dependent = await this.prisma.users.findFirst({
      where: {
        asDependent: {
          some: {
            holderId,
            dependentId,
          },
        },
      },
    });

    if (!dependent) throw new BadRequestException('Dependente não encontrado!');

    return dependent;
  }

  async getUserBenefits(id: number): Promise<BenefitInterface[] | undefined> {
    try {
      const user = await this.prisma.users.findUnique({
        where: { id },
        select: {
          plan: {
            select: {
              planBenefits: {
                select: {
                  benefits: {
                    select: {
                      id: true,
                      name: true,
                      description: true,
                    },
                  },
                },
              },
            },
          },
        },
      });

      let benefits = user?.plan?.planBenefits?.map(({ benefits }) => benefits);

      const isHolder = await this.isHolder(id);

      if (!benefits)
        throw new BadRequestException('Benefícios não encontrados');

      if (!isHolder) {
        benefits = benefits.filter(
          (benefit) => benefit.name === 'Telemedicina Humana',
        );
      }

      const benefitsOrder = [
        'Telemedicina Humana',
        'Telemedicina Pet',
        'Seguro',
        'Assistência Residencial',
        'Descontos em Medicamentos',
      ].map((item) => item.toLowerCase());

      // Reordena apenas os benefícios
      benefits.sort(
        (a, b) =>
          benefitsOrder.indexOf(a.name.toLowerCase()) -
          benefitsOrder.indexOf(b.name.toLowerCase()),
      );

      return benefits;
    } catch (error) {
      this.#logger.error(error);
      throw new BadRequestException(
        error.message || 'Erro ao encontrar benefícios',
      );
    }
  }

  /**
   * Inicia o fluxo de redefinição de senha do usuário via MFA.
   *
   * @param cpf CPF do usuário.
   * @throws {Error} Se o usuário não existir ou se não for possível enviar o e-mail.
   */
  async forgotPassword(cpf: string) {
    const user = await this.prisma.users.findUnique({ where: { cpf } });
    if (!user) throw new Error('Usuário não existe');
    if (!user.email) throw new Error('E-mail não pode ser enviado');

    // gera um número aleatório de 6 digitos
    const pinCode = (Math.floor(Math.random() * 9e5) + 1e5).toString();

    await this.prisma.$transaction(async (tx) => {
      // atualiza o código de verificação para posterior validação
      await tx.users.update({ where: { cpf }, data: { pinCode } });

      // envia o e-mail com o código de verificação por MFA
      const data = { code: pinCode, name: user.name || '', email: user.email! };
      try {
        await this.email.sendResetPassword(data);
      } catch (error) {
        throw new Error(error.message);
      }
    });

    return { message: 'E-mail enviado com sucesso!' };
  }

  async verifyPinCode(pinCode: string): Promise<boolean> {
    try {
      const isValidPin = await this.prisma.users.findFirst({
        select: { id: true },
        where: { pinCode },
      });

      return !!isValidPin;
    } catch (error) {
      throw new Error(error.message);
    }
  }

  async redefinePassword(pinCode: string, newPassword: string) {
    const user = await this.prisma.users.findFirst({ where: { pinCode } });
    if (!user) throw new Error('Código PIN incorreto');

    const hashedPassword = await this.encrypt.hashPassword(newPassword);
    try {
      await this.prisma.users.update({
        where: { pinCode, id: user.id },
        data: { password: hashedPassword, pinCode: null },
      });
    } catch (error) {
      throw new Error(error.message);
    }
  }

  /**
   * Verifica se a senha informada como parâmetro está correta.
   *
   * @param data Objeto com a senha atual, nova senha e confirmação da nova senha.
   * @param id ID do usuário.
   *
   * @returns boolean.
   */
  async verifyPassword(data: ChangePasswordDto, id: number) {
    try {
      // verifica se as senhas digitadas pelo usuário coincidem
      if (data.newPassword !== data.confirmPassword)
        throw new BadRequestException('Nova senha e confirmação não coincidem');

      const user = await this.prisma.users.findUnique({
        where: { id },
        select: { password: true },
      });

      // compara a senha digitada pelo usuário com a senha atual
      const isValidPassword = this.encrypt.comparePasswords(
        data.currentPassword,
        user!.password!,
      );

      return isValidPassword;
    } catch (error) {
      this.#logger.error('Erro ao verificar senha:', error);
      throw error;
    }
  }

  /**
   * Altera a senha do usuário
   *
   * @param id ID do usuário.
   * @param newPassword Nova senha do usuário.
   *
   * @returns Uma mensagem de confirmação de alteração de senha.
   */
  async changePassword(id: number, newPassword: string) {
    try {
      const hashedPassword = await this.encrypt.hashPassword(newPassword);

      await this.prisma.users.update({
        where: { id },
        data: { password: hashedPassword },
      });

      return { message: 'Senha alterada com sucesso!' };
    } catch (error) {
      this.#logger.error('Erro ao alterar senha:', error);
      throw error;
    }
  }

  /**
   * Faz upsert (insert ou update) de usuário TITULAR do BankSoft.
   *
   * @param user Dados do usuário titular convertidos.
   * @param planId ID do plano (só usado na inserção).
   * @param companyId ID da empresa (só usado na inserção).
   * @returns ID do usuário e se foi criado ou atualizado.
   *
   * @throws {BadRequestException} Se o CPF for inválido.
   */
  async upsertBanksoftUser(
    user: CreateUserDto,
    planId: number,
    companyId: number,
  ): Promise<{ id: number }> {
    try {
      if (user.cpf.length !== 11) throw new BadRequestException('CPF inválido');

      const userExist = await this.prisma.users.findFirst({
        where: { cpf: user.cpf },
      });

      // se usuário existir faz update
      if (userExist) {
        const renovationDate = this.getRenovationDate();

        const userData = {
          ...user,
          renovationDate: renovationDate.toISOString(),
        };

        return await this.update(userExist.id, userData);
      }

      // Cria novo usuário
      const userData = {
        ...user,
        planId,
        companyId,
      };

      const newUser = await this.createUser(userData);

      return { id: newUser.id };
    } catch (error) {
      this.#logger.error('Erro ao fazer upsert de usuário BankSoft:', error);
      if (error instanceof HttpException) throw error;
      throw new BadRequestException('Erro ao processar usuário BankSoft');
    }
  }

  /**
   * Converte dados do CreateBankSoftUserDto para CreateUserDto.
   *
   * @param bankSoftData Dados no formato BankSoft.
   * @returns Dados convertidos para o formato interno da aplicação.
   */
  convertBankSoftToCreateUser(
    bankSoftData: UpsertBankSoftUserDto,
  ): CreateUserDto {
    const user =
      bankSoftData.cadastrarCliente || (bankSoftData as any).cadastrarcliente;

    return {
      name: user.nome_cliente,
      email: user.email_cliente,
      phone: user.fonecel, // Será formatado no create/update
      cpf: user.cpfcnpj_cliente, // Será formatado no create/update
      birthDate: user.nascimento,
      gender: user.sexo.toUpperCase().charAt(0), // Converte para M ou F
      street: user.endereco_cliente,
      number: user.numendereco || 'S/N',
      zipCode: user.cep_cliente, // Será formatado no AddressService
      city: user.cidade_cliente,
      state: user.estado_cliente.toUpperCase(),
      neighborhood: user.bairro_cliente,
      complement: user.complemento || '',
    };
  }

  /**
   * Busca o plano e a empresa referentes a usuários da CCB, cadastrados
   * pela BankSoft.
   *
   * @returns Um objeto com os IDs do plano e da empresa.
   *
   * @throws {BadRequestException} Se não encontrar plano ou empresa.
   */
  async getBanksoftPlanAndCompany() {
    try {
      const plan = await this.prisma.plans.findFirst({
        where: { name: { contains: 'CCB' } },
      });

      if (!plan) throw new BadRequestException('Nenhum plano encontrado');

      const company = await this.prisma.companies.findFirst({
        where: { corporateName: { contains: 'J17' } },
      });

      if (!company) throw new BadRequestException('Nenhuma empresa encontrada');

      return { planId: plan.id, companyId: company.id };
    } catch (error) {
      this.#logger.error(error);
      if (error instanceof HttpException) throw error;
      throw new BadRequestException('Erro ao buscar plano e empresa');
    }
  }

  /**
   * Cria um usuário titular.
   *
   * @param user Dados do usuário no formato interno.
   * @returns O ID do novo usuário criado.
   *
   * @throws {BadRequestException} Se houver erro na criação do usuário ou endereço.
   */
  async createUser(user: CreateUserDto): Promise<{ id: number }> {
    try {
      return await this.prisma.$transaction(async (ctx) => {
        // Cria o usuário
        const userId = await this.create(user, ctx);

        // Cria o endereço e associa ao usuário
        await this.address.save(user, userId, ctx);

        return { id: userId };
      });
    } catch (error) {
      this.#logger.error('Erro ao criar usuário com endereço:', error);
      if (error instanceof HttpException) throw error;
      throw new BadRequestException('Erro ao criar usuário');
    }
  }

  /**
   * Lista os dependentes do usuário com base no ID do titular.
   *
   * @param id ID do titular.
   * @returns Uma lista de usuários dependentes.
   * @throws {Error} Se o usuário titular não existir.
   */
  async getUserDependents(id: number) {
    const holder = await this.prisma.users.findUnique({ where: { id } });
    if (!holder)
      throw new BadRequestException('Usuário titular não encontrado!');

    const dependents = await this.prisma.userDependents.findMany({
      where: { holderId: id },
    });

    const dependentsIds = dependents.map((dep) => dep.dependentId);
    dependentsIds.push(id);

    const users = await this.prisma.users.findMany({
      where: { id: { in: dependentsIds } },
      select: {
        id: true,
        name: true,
        cpf: true,
        email: true,
        birthDate: true,
        gender: true,
        phone: true,
        createdAt: true,
        userAddress: {
          include: {
            address: {
              include: {
                city: true,
              },
            },
          },
        },
        password: false,
      },
    });

    return users.map((user) => ({
      ...user,
      // verifica se o usuário é titular
      isHolder: user.id === id,
    }));
  }

  /**
   * Verifica se o usuário é um titular ou dependente pela quantidade de dependentes.
   *
   * @param userId Identificador do usuário na base de dados.
   * @returns A confirmação da titularidade do usuário.
   */
  async isHolder(userId: number) {
    const dependent = await this.prisma.userDependents.count({
      where: { dependentId: userId },
    });

    // Retorna true se o count for zero
    return !dependent;
  }

  /**
   * Remove um usuário dependente da base de dados (aplicando soft delete).
   *
   * @param holderId ID do usuário titular.
   * @param dependentId ID do usuário dependente que será removido.
   */
  async removeDependent(holderId: number, dependentId: number) {
    const dependent = await this.prisma.userDependents.findFirst({
      where: { holderId, dependentId },
    });

    if (!dependent) throw new NotFoundException('Dependente não encontrado!');

    await this.prisma.$transaction(async (tx) => {
      const telemedUser = await tx.telemedUsers.findFirst({
        where: { userId: dependentId },
      });

      if (telemedUser)
        await tx.telemedUsers.delete({
          where: { userId: dependentId },
        });

      await tx.userDependents.deleteMany({
        where: { dependentId },
      });

      await tx.users.delete({
        where: { id: dependentId },
      });
    });

    return { message: 'Usuário removido com sucesso!' };
  }

  /**
   * Busca o ID da empresa padrão.
   *
   * @returns ID da empresa padrão.
   * @throws {NotFoundException} Se a empresa padrão não for encontrada.
   */
  async getDefaultCompanyId(): Promise<number> {
    const company = await this.prisma.companies.findFirst({
      where: { corporateName: { contains: 'J17' } },
    });

    if (!company) throw new NotFoundException('Empresa padrão não encontrada');

    return company.id;
  }

  /**
   * Busca o ID do plano padrão para uma empresa específica.
   * @param companyId ID da empresa para buscar o plano padrão.
   * @returns ID do plano padrão.
   * @throws {NotFoundException} Se o plano padrão não for encontrado.
   */
  async getDefaultPlanId(companyId: number): Promise<number> {
    const plan = await this.prisma.plans.findFirst({
      where: { companyId, isDefault: true },
    });

    if (!plan) throw new NotFoundException('Plano padrão não encontrado');

    return plan.id;
  }
}
