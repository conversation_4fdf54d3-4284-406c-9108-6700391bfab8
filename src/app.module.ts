import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { APP_INTERCEPTOR } from '@nestjs/core';
import { FilesModule } from './files/files.module';

import AuthModule from './auth/auth.module';
import NotificationsModule from './notifications/notifications.module';
import AddressModule from './address/address.module';
import KYCModule from './kyc/kyc.module';
import IntegrationsModule from './integrations/integrations.module';
import UserModule from './user/user.module';

import PrismaModule from './utils/prisma/prisma.module';
import EncryptModule from './utils/encrypt/encrypt.module';
import HealthcheckModule from './utils/healthcheck/healthcheck.module';
import EncryptService from './utils/encrypt/encrypt.service';
import JweInterceptor from './utils/encrypt/jwe.interceptor';
import NewRelicInterceptor from './utils/new-relic/new-relic.interceptor';
import NewRelicModule from './utils/new-relic/new-relic.module';

import configuration from './config/configuration';
import UnicoModule from './kyc/unico.module';

@Module({
  imports: [
    PrismaModule,
    AuthModule,
    UserModule,
    KYCModule,
    AddressModule,
    ConfigModule.forRoot({ load: [configuration] }),
    IntegrationsModule,
    EncryptModule,
    HealthcheckModule,
    NewRelicModule,
    NotificationsModule,
    FilesModule,
    UnicoModule,
  ],
  providers: [
    {
      provide: APP_INTERCEPTOR,
      useClass: NewRelicInterceptor,
    },
    {
      provide: APP_INTERCEPTOR,
      inject: [EncryptService],
      useFactory: (encrypt: EncryptService) => new JweInterceptor(encrypt),
    },
  ],
})
export default class AppModule {}
