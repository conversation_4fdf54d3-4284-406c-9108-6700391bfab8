import 'dotenv/config';

// TODO: forçar a validação dos tipos de cada secret
export default () => ({
  app: {
    env: process.env.NODE_ENV || ('local' as const),
    port: process.env.PORT || 3000,
    jwt: {
      secret: process.env.JWT_SECRET || 'secret',
      expiration: process.env.JWT_EXPIRATION || 60,
    },
    jwe: { secret: process.env.JWE_SECRET || 'secret' },
  },
  integrations: {
    topmed: {
      id: process.env.TOPMED_J17_ID,
      code: process.env.TOPMED_J17_CODE,
      url: process.env.TOPMED_URL || 'https://lhapi.dev.nextplus.com.br',
      username: process.env.TOPMED_USER,
      password: process.env.TOPMED_PASS,
      'wehbook-username': process.env.TOPMED_WEBHOOK_USERNAME,
      'webhook-password': process.env.TOPMED_WEBHOOK_PASSWORD,
      'allowed-ips': process.env.TOPMED_ALLOWED_IPS,
    },
    televets: {
      customer: process.env.TELEVETS_CUSTOMER,
      plan: process.env.TELEVETS_PLAN,
      iframeUrl: process.env.TELEVETS_IFRAME_URL,
    },
  },
  kyc: {
    unico: {
      id: {
        url: process.env.UNICO_ID_URL,
        tenant: process.env.UNICO_ID_TENANT,
        'private-key': process.env.UNICO_ID_PRIVATE_KEY,
      },
      message: {
        url: process.env.UNICO_MESSAGE_URL,
        token: process.env.UNICO_MESSAGE_TOKEN,
      },
    },
  },
  'new-relic': {
    name: process.env.NEW_RELIC_APP_NAME,
    key: process.env.NEW_RELIC_LICENSE_KEY,
  },
  notifications: {
    email: {
      host: process.env.EMAIL_HOST,
      port: process.env.EMAIL_PORT,
      sender: process.env.EMAIL_SENDER,
      secure: false,
      auth: {
        user: process.env.EMAIL_USER,
        password: process.env.EMAIL_PASS,
      },
    },
    firebase: {
      projectId: process.env.FIREBASE_PROJECT_ID,
      privateKey: process.env.FIREBASE_PRIVATE_KEY,
      clientEmail: process.env.FIREBASE_CLIENT_EMAIL,
    },
  },
  aws: {
    ses: {
      region: process.env.AWS_REGION,
      'access-key': process.env.AWS_ACCESS_KEY,
      'secret-key': process.env.AWS_SECRET_KEY,
    },
    s3: {
      bucket: process.env.AWS_S3_BUCKET,
      'access-key': process.env.AWS_S3_ACCESS_KEY,
      'secret-key': process.env.AWS_S3_SECRET_KEY,
    },
  },
});
