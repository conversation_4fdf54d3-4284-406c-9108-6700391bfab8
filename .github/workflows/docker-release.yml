name: docker-release

on:
  push:
    tags:
      - 'v*'

jobs:
  build-and-push-image:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.ACCESS_KEY }}
          aws-secret-access-key: ${{ secrets.SECRET_KEY }}
          aws-region: ${{ secrets.AWS_REGION }}

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v2

      - uses: chrnorm/deployment-action@v2
        name: Create GitHub deployment
        id: deployment
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          description: 'Production'
          environment: production

      - name: Configura Docker Buildx
        id: buildx
        uses: docker/setup-buildx-action@v3

      - name: Extract metadata (tags, labels) for Docker
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ steps.login-ecr.outputs.registry }}/${{ vars.REPO_NAME_DEV }}
          tags: |
            type=semver,pattern={{version}}

      - name: Build, tag, and push the image to Amazon ECR
        uses: docker/build-push-action@v6
        with:
          push: true
          pull: true
          provenance: false
          context: .
          file: .docker/Dockerfile
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          cache-from: type=gha
          cache-to: type=gha,mode=max

      - name: Update deployment status (success)
        if: success()
        uses: chrnorm/deployment-status@v2
        with:
          token: '${{ secrets.GITHUB_TOKEN }}'
          deployment-id: ${{ steps.deployment.outputs.deployment_id }}
          environment-url: https://api.aliviae.com.vc
          state: 'success'

      - name: Update deployment status (failure)
        if: failure()
        uses: chrnorm/deployment-status@v2
        with:
          token: '${{ secrets.GITHUB_TOKEN }}'
          deployment-id: ${{ steps.deployment.outputs.deployment_id }}
          environment-url: https://api.aliviae.com.vc
          state: 'failure'

      - name: Create Release
        id: create_release
        uses: actions/create-release@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          tag_name: ${{ github.ref }}
          release_name: ${{ github.ref }}
          draft: false
          prerelease: false
