name: Development Pipeline

on:
  workflow_dispatch:
  push:
    branches: [ develop ]

jobs:
  test:
    name: Verifica condições de deploy
    runs-on: ubuntu-latest
    environment: development
    concurrency:
      group: ${{ github.ref }}
      cancel-in-progress: true

    strategy:
      matrix:
        node-version: [ 22 ]

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Realiza a instalação das dependências
        uses: actions/setup-node@v4
        with:
          node-version: 22
          cache: yarn
      - run: yarn --frozen-lockfile --silent && yarn prisma generate --schema prisma/schema

      - name: Executa os testes da aplicação
        run: yarn test --ci --maxWorkers=50%

      - name: Verifica a formatação e estrutura do código
        run: yarn prettier --check ./src

      - name: Verifica a existência de erros e inconsistências
        run: yarn eslint --max-warnings=0 ./src

#      - name: Aplica as migrations no banco de desenvolvimento
#        env:
#          NODE_ENV: development
#          DATABASE_URL: ${{ secrets.DATABASE_URL_DEV }}
#        run: yarn prisma migrate deploy

  deploy:
    name: Envia para o AWS ECR
    runs-on: ubuntu-latest
    environment: development
    needs:
      - test

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Configura Docker Buildx
        id: buildx
        uses: docker/setup-buildx-action@v3

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.ACCESS_KEY }}
          aws-secret-access-key: ${{ secrets.SECRET_KEY }}
          aws-region: ${{ secrets.AWS_REGION }}

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v2

      - uses: chrnorm/deployment-action@v2
        name: Create GitHub deployment
        id: deployment
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          description: 'Development'
          environment: development
          environment-url: https://api.dev.aliviae.com.vc

      - name: Build, tag, and push the image to Amazon ECR
        uses: docker/build-push-action@v6
        with:
          push: true
          pull: true
          provenance: false
          context: .
          file: .docker/Dockerfile
          tags: "${{ steps.login-ecr.outputs.registry }}/${{ vars.REPO_NAME_DEV }}:latest"
          cache-from: type=gha
          cache-to: type=gha,mode=max

      - name: Update deployment status (success)
        if: success()
        uses: chrnorm/deployment-status@v2
        with:
          token: '${{ secrets.GITHUB_TOKEN }}'
          deployment-id: ${{ steps.deployment.outputs.deployment_id }}
          state: 'success'

      - name: Update deployment status (failure)
        if: failure()
        uses: chrnorm/deployment-status@v2
        with:
          token: '${{ secrets.GITHUB_TOKEN }}'
          deployment-id: ${{ steps.deployment.outputs.deployment_id }}
          state: 'failure'
          description: 'Falha ao realizar o build da imagem'
