name: Pull Request Pipeline

on:
  pull_request:
    branches: [ develop ]

jobs:
  analyse:
    name: Revisa critérios de aceitação de pull request
    runs-on: ubuntu-latest

    strategy:
      matrix:
        node-version: [ 22 ]

    steps:
      - uses: actions/checkout@v4

      - name: Realiza a instalação das dependências
        uses: actions/setup-node@v4
        with:
          node-version: 22
          cache: yarn
      - run: yarn --frozen-lockfile --silent && yarn prisma generate --schema prisma/schema

      - name: Executa os testes da aplicação
        run: yarn test --ci --maxWorkers=50%

      - name: Verifica a formatação e estrutura do código
        run: yarn prettier --check ./src

      - name: Verifica a existência de erros e inconsistências
        run: yarn eslint --max-warnings=0 ./src
