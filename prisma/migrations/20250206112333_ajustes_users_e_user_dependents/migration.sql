/*
  Warnings:

  - You are about to drop the `users_dependents` table. If the table is not empty, all the data it contains will be lost.

*/
-- DropForeignKey
ALTER TABLE "users_dependents" DROP CONSTRAINT "users_dependents_dependent_id_fkey";

-- DropForeignKey
ALTER TABLE "users_dependents" DROP CONSTRAINT "users_dependents_holder_id_fkey";

-- AlterTable
ALTER TABLE "users" ALTER COLUMN "phone" DROP NOT NULL,
ALTER COLUMN "password" DROP NOT NULL;

-- DropTable
DROP TABLE "users_dependents";

-- CreateTable
CREATE TABLE "user_dependents" (
    "id" SERIAL NOT NULL,
    "holder_id" INTEGER NOT NULL,
    "dependent_id" INTEGER NOT NULL,

    CONSTRAINT "user_dependents_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "user_dependents_holder_id_dependent_id_key" ON "user_dependents"("holder_id", "dependent_id");

-- AddForeignKey
ALTER TABLE "user_dependents" ADD CONSTRAINT "user_dependents_holder_id_fkey" FOREIGN KEY ("holder_id") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "user_dependents" ADD CONSTRAINT "user_dependents_dependent_id_fkey" FOREIGN KEY ("dependent_id") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
