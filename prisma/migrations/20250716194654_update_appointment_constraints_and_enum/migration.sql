/*
  Warnings:

  - A unique constraint covering the columns `[external_id,telemed_user_id]` on the table `telemed_appointments` will be added. If there are existing duplicate values, this will fail.

*/
-- AlterEnum
ALTER TYPE "Status" ADD VALUE 'CANCELLED';

-- DropIndex
DROP INDEX "telemed_appointments_external_id_telemed_user_id_status_nam_key";

-- CreateIndex
CREATE UNIQUE INDEX "telemed_appointments_external_id_telemed_user_id_key" ON "telemed_appointments"("external_id", "telemed_user_id");
