-- CreateEnum
CREATE TYPE "Status" AS ENUM ('SCHEDULED', 'CANCELED', 'PENDING', 'COMPLETED');

-- CreateTable
CREATE TABLE "plans" (
    "id" SERIAL NOT NULL,
    "name" VARCHAR(255) NOT NULL,
    "description" TEXT,
    "price" DECIMAL(65,30) NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "plans_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "benefits" (
    "id" SERIAL NOT NULL,
    "supplier_id" INTEGER NOT NULL,
    "name" VARCHAR(255) NOT NULL,
    "description" TEXT NOT NULL,
    "price" DECIMAL(65,30) NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "benefits_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "plan_benefits" (
    "id" SERIAL NOT NULL,
    "plan_id" INTEGER NOT NULL,
    "benefit_id" INTEGER NOT NULL,

    CONSTRAINT "plan_benefits_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "suppliers" (
    "id" SERIAL NOT NULL,
    "company_id" INTEGER NOT NULL,
    "description" TEXT NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "suppliers_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "companies" (
    "id" SERIAL NOT NULL,
    "cnpj" CHAR(14) NOT NULL,
    "corporate_name" VARCHAR(255),
    "fantasy_name" VARCHAR(255) NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "companies_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "telemed_users" (
    "id" SERIAL NOT NULL,
    "supplier_id" INTEGER NOT NULL,
    "user_id" INTEGER NOT NULL,
    "user_telemed_id" VARCHAR(255) NOT NULL,
    "registered_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "telemed_users_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "telemed_settings" (
    "id" SERIAL NOT NULL,
    "telemed_user_id" INTEGER NOT NULL,
    "name" VARCHAR(255) NOT NULL,
    "description" TEXT NOT NULL,
    "value" JSONB NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "telemed_settings_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "telemed_appointments" (
    "id" SERIAL NOT NULL,
    "external_id" VARCHAR(25) NOT NULL,
    "telemed_user_id" INTEGER NOT NULL,
    "status_name" "Status" NOT NULL DEFAULT 'SCHEDULED',
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "telemed_appointments_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "appointment_status" (
    "name" "Status" NOT NULL,
    "description" TEXT NOT NULL
);

-- CreateTable
CREATE TABLE "televet_users" (
    "id" SERIAL NOT NULL,
    "supplier_id" INTEGER NOT NULL,
    "user_id" INTEGER NOT NULL,
    "user_televet_id" VARCHAR(255),
    "registered_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "televet_users_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "televet_user_pets" (
    "id" SERIAL NOT NULL,
    "televet_user_id" INTEGER NOT NULL,
    "pet_televet_id" VARCHAR(255),
    "name" VARCHAR(255) NOT NULL,

    CONSTRAINT "televet_user_pets_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "televet_settings" (
    "id" SERIAL NOT NULL,
    "televet_user_id" INTEGER NOT NULL,
    "name" VARCHAR(255) NOT NULL,
    "description" TEXT NOT NULL,
    "value" JSONB NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "televet_settings_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "televet_appointments" (
    "id" SERIAL NOT NULL,
    "external_id" VARCHAR(255) NOT NULL,
    "televet_user_pet_id" INTEGER NOT NULL,
    "status_name" "Status" NOT NULL DEFAULT 'SCHEDULED',
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "televet_appointments_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "users" (
    "id" SERIAL NOT NULL,
    "name" VARCHAR(255) NOT NULL,
    "cpf" CHAR(11) NOT NULL,
    "birth_date" DATE NOT NULL,
    "renovation_date" DATE NOT NULL,
    "gender" CHAR(1) NOT NULL,
    "phone" CHAR(11) NOT NULL,
    "email" VARCHAR(255),
    "password" VARCHAR(255) NOT NULL,
    "pin_code" CHAR(6),
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "deleted_at" TIMESTAMP(3),
    "plan_id" INTEGER,
    "company_id" INTEGER NOT NULL,

    CONSTRAINT "users_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "users_dependents" (
    "id" SERIAL NOT NULL,
    "holder_id" INTEGER NOT NULL,
    "dependent_id" INTEGER NOT NULL,

    CONSTRAINT "users_dependents_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "document_proccess" (
    "process_id" UUID NOT NULL,
    "user_id" INTEGER NOT NULL
);

-- CreateTable
CREATE TABLE "files_types" (
    "id" SERIAL NOT NULL,
    "name" VARCHAR(255) NOT NULL,
    "description" TEXT NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "files_types_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "user_files" (
    "id" SERIAL NOT NULL,
    "type_id" INTEGER NOT NULL,
    "user_id" INTEGER NOT NULL,
    "path" VARCHAR(255) NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "user_files_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "user_benefits" (
    "id" SERIAL NOT NULL,
    "benefit_id" INTEGER NOT NULL,
    "user_id" INTEGER NOT NULL,

    CONSTRAINT "user_benefits_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "cities" (
    "code" CHAR(7) NOT NULL,
    "name" VARCHAR(255) NOT NULL,
    "state" VARCHAR(255) NOT NULL,
    "state_acronym" CHAR(2) NOT NULL,

    CONSTRAINT "cities_pkey" PRIMARY KEY ("code")
);

-- CreateTable
CREATE TABLE "addresses" (
    "id" SERIAL NOT NULL,
    "city_code" CHAR(7) NOT NULL,
    "street" VARCHAR(255) NOT NULL,
    "number" VARCHAR(7) NOT NULL,
    "neighborhood" TEXT NOT NULL,
    "zip_code" VARCHAR(255) NOT NULL,
    "complement" TEXT,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "addresses_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "user_addresses" (
    "id" SERIAL NOT NULL,
    "user_id" INTEGER NOT NULL,
    "address_id" INTEGER NOT NULL,
    "home_insurance" BOOLEAN NOT NULL DEFAULT false,

    CONSTRAINT "user_addresses_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "suppliers_company_id_key" ON "suppliers"("company_id");

-- CreateIndex
CREATE UNIQUE INDEX "companies_cnpj_key" ON "companies"("cnpj");

-- CreateIndex
CREATE UNIQUE INDEX "telemed_users_user_id_key" ON "telemed_users"("user_id");

-- CreateIndex
CREATE UNIQUE INDEX "telemed_users_user_id_user_telemed_id_key" ON "telemed_users"("user_id", "user_telemed_id");

-- CreateIndex
CREATE UNIQUE INDEX "telemed_appointments_external_id_telemed_user_id_key" ON "telemed_appointments"("external_id", "telemed_user_id");

-- CreateIndex
CREATE UNIQUE INDEX "appointment_status_name_key" ON "appointment_status"("name");

-- CreateIndex
CREATE UNIQUE INDEX "televet_users_user_id_key" ON "televet_users"("user_id");

-- CreateIndex
CREATE UNIQUE INDEX "televet_users_supplier_id_user_id_key" ON "televet_users"("supplier_id", "user_id");

-- CreateIndex
CREATE UNIQUE INDEX "televet_user_pets_pet_televet_id_key" ON "televet_user_pets"("pet_televet_id");

-- CreateIndex
CREATE UNIQUE INDEX "users_cpf_key" ON "users"("cpf");

-- CreateIndex
CREATE UNIQUE INDEX "users_phone_key" ON "users"("phone");

-- CreateIndex
CREATE UNIQUE INDEX "users_email_key" ON "users"("email");

-- CreateIndex
CREATE UNIQUE INDEX "users_dependents_holder_id_dependent_id_key" ON "users_dependents"("holder_id", "dependent_id");

-- CreateIndex
CREATE UNIQUE INDEX "document_proccess_process_id_key" ON "document_proccess"("process_id");

-- CreateIndex
CREATE UNIQUE INDEX "document_proccess_user_id_key" ON "document_proccess"("user_id");

-- CreateIndex
CREATE UNIQUE INDEX "document_proccess_process_id_user_id_key" ON "document_proccess"("process_id", "user_id");

-- CreateIndex
CREATE UNIQUE INDEX "user_benefits_user_id_benefit_id_key" ON "user_benefits"("user_id", "benefit_id");

-- AddForeignKey
ALTER TABLE "benefits" ADD CONSTRAINT "benefits_supplier_id_fkey" FOREIGN KEY ("supplier_id") REFERENCES "suppliers"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "plan_benefits" ADD CONSTRAINT "plan_benefits_plan_id_fkey" FOREIGN KEY ("plan_id") REFERENCES "plans"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "plan_benefits" ADD CONSTRAINT "plan_benefits_benefit_id_fkey" FOREIGN KEY ("benefit_id") REFERENCES "benefits"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "suppliers" ADD CONSTRAINT "suppliers_company_id_fkey" FOREIGN KEY ("company_id") REFERENCES "companies"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "telemed_users" ADD CONSTRAINT "telemed_users_supplier_id_fkey" FOREIGN KEY ("supplier_id") REFERENCES "suppliers"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "telemed_users" ADD CONSTRAINT "telemed_users_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "telemed_settings" ADD CONSTRAINT "telemed_settings_telemed_user_id_fkey" FOREIGN KEY ("telemed_user_id") REFERENCES "telemed_users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "telemed_appointments" ADD CONSTRAINT "telemed_appointments_telemed_user_id_fkey" FOREIGN KEY ("telemed_user_id") REFERENCES "telemed_users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "telemed_appointments" ADD CONSTRAINT "telemed_appointments_status_name_fkey" FOREIGN KEY ("status_name") REFERENCES "appointment_status"("name") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "televet_users" ADD CONSTRAINT "televet_users_supplier_id_fkey" FOREIGN KEY ("supplier_id") REFERENCES "suppliers"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "televet_users" ADD CONSTRAINT "televet_users_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "televet_user_pets" ADD CONSTRAINT "televet_user_pets_televet_user_id_fkey" FOREIGN KEY ("televet_user_id") REFERENCES "televet_users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "televet_settings" ADD CONSTRAINT "televet_settings_televet_user_id_fkey" FOREIGN KEY ("televet_user_id") REFERENCES "televet_users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "televet_appointments" ADD CONSTRAINT "televet_appointments_televet_user_pet_id_fkey" FOREIGN KEY ("televet_user_pet_id") REFERENCES "televet_user_pets"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "televet_appointments" ADD CONSTRAINT "televet_appointments_status_name_fkey" FOREIGN KEY ("status_name") REFERENCES "appointment_status"("name") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "users" ADD CONSTRAINT "users_company_id_fkey" FOREIGN KEY ("company_id") REFERENCES "companies"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "users" ADD CONSTRAINT "users_plan_id_fkey" FOREIGN KEY ("plan_id") REFERENCES "plans"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "users_dependents" ADD CONSTRAINT "users_dependents_holder_id_fkey" FOREIGN KEY ("holder_id") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "users_dependents" ADD CONSTRAINT "users_dependents_dependent_id_fkey" FOREIGN KEY ("dependent_id") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "document_proccess" ADD CONSTRAINT "document_proccess_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "user_files" ADD CONSTRAINT "user_files_type_id_fkey" FOREIGN KEY ("type_id") REFERENCES "files_types"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "user_files" ADD CONSTRAINT "user_files_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "user_benefits" ADD CONSTRAINT "user_benefits_benefit_id_fkey" FOREIGN KEY ("benefit_id") REFERENCES "benefits"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "user_benefits" ADD CONSTRAINT "user_benefits_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "addresses" ADD CONSTRAINT "addresses_city_code_fkey" FOREIGN KEY ("city_code") REFERENCES "cities"("code") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "user_addresses" ADD CONSTRAINT "user_addresses_address_id_fkey" FOREIGN KEY ("address_id") REFERENCES "addresses"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "user_addresses" ADD CONSTRAINT "user_addresses_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
