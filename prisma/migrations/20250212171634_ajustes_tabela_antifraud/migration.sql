/*
  Warnings:

  - You are about to drop the `document_proccess` table. If the table is not empty, all the data it contains will be lost.

*/
-- DropForeignKey
ALTER TABLE "document_proccess" DROP CONSTRAINT "document_proccess_user_id_fkey";

-- DropTable
DROP TABLE "document_proccess";

-- CreateTable
CREATE TABLE "antifraud_proccess" (
    "process_id" UUID NOT NULL,
    "user_id" INTEGER NOT NULL,
    "score" INTEGER NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL
);

-- CreateIndex
CREATE UNIQUE INDEX "antifraud_proccess_process_id_key" ON "antifraud_proccess"("process_id");

-- CreateIndex
CREATE UNIQUE INDEX "antifraud_proccess_user_id_key" ON "antifraud_proccess"("user_id");

-- Add<PERSON><PERSON><PERSON><PERSON>ey
ALTER TABLE "antifraud_proccess" ADD CONSTRAINT "antifraud_proccess_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
