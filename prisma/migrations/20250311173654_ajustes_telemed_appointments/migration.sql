/*
  Warnings:

  - A unique constraint covering the columns `[external_id,telemed_user_id,status_name]` on the table `telemed_appointments` will be added. If there are existing duplicate values, this will fail.

*/
-- DropIndex
DROP INDEX "telemed_appointments_external_id_telemed_user_id_key";

-- AlterTable
ALTER TABLE "telemed_appointments" ALTER COLUMN "appointment_date" DROP NOT NULL;

-- CreateIndex
CREATE UNIQUE INDEX "telemed_appointments_external_id_telemed_user_id_status_nam_key" ON "telemed_appointments"("external_id", "telemed_user_id", "status_name");
