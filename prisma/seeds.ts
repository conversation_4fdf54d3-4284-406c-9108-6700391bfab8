import { PrismaClient } from '@prisma/client';

import { Faker, pl, pt_BR } from '@faker-js/faker';

const faker = new Faker({ locale: [pt_BR] });

const prisma = new PrismaClient();

async function main() {
  const cities = [
    'Curitiba',
    'São Paulo',
    'Rio de Janeiro',
    'Belo Horizonte',
    'Brasília',
  ];
  const states = [
    'Paraná',
    'São Paulo',
    'Rio de Janeiro',
    'Minas Gerais',
    'Distrito Federal',
  ];
  const stateAcronym = ['PR', 'SP', 'RJ', 'MG', 'DF'];

  // Seed para Cities
  await prisma.cities.createMany({
    data: Array.from({ length: 5 }, (_, index) => ({
      code: faker.string.numeric(5),
      name: cities[index],
      state: states[index],
      stateAcronym: stateAcronym[index],
    })),
  });

  const city = await prisma.cities.findMany();

  // Seed para Addresses
  await prisma.addresses.createMany({
    data: Array.from({ length: 5 }, (_, index) => ({
      cityCode: city[index].code,
      street: faker.location.street(),
      number: faker.location.buildingNumber(),
      zipCode: faker.location.zipCode(),
      complement: faker.location.city(),
      neighborhood: faker.location.city(),
    })),
  });

  // Seed para Plans
  const planNames = ['Bronze', 'Prata', 'Ouro'];
  await prisma.plans.createMany({
    data: Array.from({ length: planNames.length }, (_, index) => ({
      name: planNames[index],
      description: 'Plano do usuário',
      price: faker.number.float({ min: 50, max: 300 }),
    })),
  });

  // Seed para Companies
  await prisma.companies.createMany({
    data: Array.from({ length: 2 }, () => ({
      cnpj: faker.string.numeric(14).slice(0, 14),
      corporateName: faker.company.name().slice(0, 200),
      fantasyName: faker.company.name().slice(0, 200),
    })),
  });

  const companies = await prisma.companies.findMany();

  // Seed para Suppliers
  await prisma.suppliers.createMany({
    data: Array.from({ length: 1 }, () => ({
      description: 'Serviços de telemedicina, televet e seguros',
      companyId: companies[0].id,
    })),
  });

  const suppliers = await prisma.suppliers.findMany();

  // Seed para Benefits
  const benefitsNames = ['Telemedicina Humana', 'Televet', 'Seguro'];
  await prisma.benefits.createMany({
    data: Array.from({ length: benefitsNames.length }, (_, index) => ({
      name: benefitsNames[index],
      description: faker.company.name(),
      price: faker.number.float({ min: 10, max: 500 }),
      supplierId: suppliers[0].id,
    })),
  });

  const benefits = await prisma.benefits.findMany();
  const plan = await prisma.plans.findMany();

  // tabela de agragação planBenefits
  await prisma.planBenefits.createMany({
    data: plan.flatMap((plan, index) => {
      const numBenefits = index + 1;
      const selectedBenefits = benefits.slice(0, numBenefits);
      return selectedBenefits.map((benefit) => ({
        planId: plan.id,
        benefitId: benefit.id,
      }));
    }),
  });

  // Seed para Users
  try {
    await prisma.users.createMany({
      data: Array.from({ length: 5 }, () => ({
        name: faker.person.fullName(),
        email: faker.internet.email(),
        phone: faker.string.numeric(11),
        cpf: faker.string.numeric(11),
        birthDate: faker.date.birthdate(),
        renovationDate: faker.date.future(),
        gender: Math.random() < 0.5 ? 'M' : 'F',
        companyId: companies[0].id,
        planId: plan[0].id,
      })),
    });
  } catch (error) {
    console.error(error);
  }

  const users = await prisma.users.findMany();
  const addresses = await prisma.addresses.findMany();

  // tabela de agregação userAddresses
  await prisma.userAddresses.createMany({
    data: users.map((user, index) => ({
      userId: user.id,
      addressId: addresses[index % addresses.length].id, // Atribui um endereço aleatório do array de endereços, repetindo o ciclo se necessário
    })),
  });

  /**
   * Popula a tabela userDependents com relacionamentos entre usuários
   * titulares (holders) e seus dependentes.
   *
   * A população é feita de forma que cada holder tenha
   * pelo menos 1 dependent e no máximo 3.
   *
   * A seleção dos dependentes é feita de forma aleatória
   * e única, ou seja, um dependent não pode estar associado
   * a mais de um holder.
   */
  async function seedUserDependents() {
    const holdersCount = Math.floor(users.length / 2); // Metade dos usuários são holders
    const holders = users.slice(0, holdersCount);
    let dependentsPool = users.slice(holdersCount); // Restante são dependents

    const userDependents = holders.flatMap((holder) => {
      const maxDependents = 3;
      const selectedDependents = dependentsPool.slice(0, maxDependents); // Seleciona os dependents disponíveis
      dependentsPool = dependentsPool.slice(maxDependents); // Remove os selecionados da pool

      return selectedDependents.map((dependent) => ({
        holderId: holder.id,
        dependentId: dependent.id,
      }));
    });

    await prisma.userDependents.createMany({ data: userDependents });
    console.log('User dependents seeded successfully!');
  }

  seedUserDependents();
}

main()
  .then(async () => {
    await prisma.$disconnect();
  })
  .catch(async (e) => {
    console.error(e);
    await prisma.$disconnect();
    process.exit(1);
  });
