model Plans {
  id          Int      @id @default(autoincrement())
  name        String   @db.VarChar(255)
  description String?  @db.Text
  price       Decimal  @db.Money
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")

  planBenefits PlanBenefits[]
  user         Users[]

  @@map("plans")
}

model Benefits {
  id          Int      @id @default(autoincrement())
  supplierId  Int      @map("supplier_id")
  name        String   @db.VarChar(255)
  description String   @db.Text
  price       Decimal  @db.Money
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")

  supplier Suppliers @relation(fields: [supplierId], references: [id])

  PlanBenefits PlanBenefits[]
  UserBenefits UserBenefits[]

  @@map("benefits")
}

model PlanBenefits {
  id        Int @id @default(autoincrement())
  planId    Int @map("plan_id")
  benefitId Int @map("benefit_id")

  plans    Plans    @relation(fields: [planId], references: [id])
  benefits Benefits @relation(fields: [benefitId], references: [id])

  @@map("plan_benefits")
}

model Suppliers {
  id          Int      @id @default(autoincrement())
  companyId   Int      @unique @map("company_id")
  description String   @db.Text
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")

  companies Companies @relation(fields: [companyId], references: [id])

  benefits     Benefits[]
  telemedUsers TelemedUsers[]
  televetUsers TelevetUsers[]

  @@map("suppliers")
}
