model TelevetUsers {
  id            Int       @id @default(autoincrement())
  supplierId    Int       @map("supplier_id")
  userId        Int       @unique @map("user_id")
  userTelevetId String?   @map("user_televet_id") @db.VarChar(255)
  registeredAt  DateTime  @default(now()) @map("registered_at")
  deletedAt     DateTime? @map("deleted_at")

  supplier Suppliers @relation(fields: [supplierId], references: [id])
  user     Users     @relation(fields: [userId], references: [id])

  TelevetSettings TelevetSettings[]
  TelevetUserPets TelevetUserPets[]

  @@unique([supplierId, userId])
  @@map("televet_users")
}

model TelevetUserPets {
  id            Int     @id @default(autoincrement())
  televetUserId Int     @map("televet_user_id")
  petTelevetId  String? @unique @map("pet_televet_id") @db.VarChar(255)
  name          String  @db.VarChar(255)

  televetUser TelevetUsers @relation(fields: [televetUserId], references: [id])

  televetAppointments TelevetAppointments[]

  @@map("televet_user_pets")
}

// Configurações de usuário
model TelevetSettings {
  id            Int      @id @default(autoincrement())
  televetUserId Int      @map("televet_user_id")
  name          String   @db.VarChar(255)
  description   String   @db.Text
  value         Json     @db.JsonB
  createdAt     DateTime @default(now()) @map("created_at")
  updatedAt     DateTime @updatedAt @map("updated_at")

  televetUser TelevetUsers @relation(fields: [televetUserId], references: [id])

  @@map("televet_settings")
}

// Consultas realizadas
model TelevetAppointments {
  id               Int      @id @default(autoincrement())
  externalId       String?  @map("external_id") @db.VarChar(255)
  televetUserPetId Int      @map("televet_user_pet_id")
  statusName       Status   @default(SCHEDULED) @map("status_name")
  appointmentDate  DateTime @map("appointment_date")
  createdAt        DateTime @default(now()) @map("created_at")
  updatedAt        DateTime @updatedAt @map("updated_at")

  televetUserPet TelevetUserPets   @relation(fields: [televetUserPetId], references: [id])
  status         AppointmentStatus @relation(fields: [statusName], references: [name])

  @@map("televet_appointments")
}
