model Users {
  id             Int       @id @default(autoincrement())
  name           String    @db.Var<PERSON>har(255)
  cpf            String    @unique @db.Char(11)
  birthDate      DateTime  @map("birth_date") @db.Date
  renovationDate DateTime  @map("renovation_date") @db.Date
  gender         String    @db.Char(1)
  phone          String?   @db.Char(11)
  email          String?   @db.VarChar(255)
  password       String?   @db.VarChar(255)
  pinCode        String?   @map("pin_code") @db.Char(6)
  createdAt      DateTime  @default(now()) @map("created_at")
  updatedAt      DateTime  @updatedAt @map("updated_at")
  deletedAt      DateTime? @map("deleted_at")
  planId         Int?      @map("plan_id")
  companyId      Int       @map("company_id")

  usersFiles        UserFiles[]
  telemedUsers      TelemedUsers?
  antifraudProccess AntifraudProccess?
  televetUsers      TelevetUsers?
  userAddress       UserAddresses[]
  userBenefits      UserBenefits[]
  asHolder          UserDependents[]   @relation("HolderRelation")
  asDependent       UserDependents[]   @relation("DependentRelation")

  companies Companies @relation(fields: [companyId], references: [id])
  plan      Plans?    @relation(fields: [planId], references: [id])

  @@index([email])
  @@map("users")
}

model UserDependents {
  id          Int       @id @default(autoincrement())
  holderId    Int       @map("holder_id")
  dependentId Int       @map("dependent_id")
  deletedAt   DateTime? @map("deleted_at")

  holder    Users @relation("HolderRelation", fields: [holderId], references: [id])
  dependent Users @relation("DependentRelation", fields: [dependentId], references: [id])

  @@unique([holderId, dependentId])
  @@map("user_dependents")
}

model AntifraudProccess {
  processId String   @unique @map("process_id") @db.Uuid
  userId    Int      @unique @map("user_id")
  score     Int
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  user Users @relation(fields: [userId], references: [id])

  @@map("antifraud_proccess")
}

model FileTypes {
  id          Int      @id @default(autoincrement())
  name        String   @db.VarChar(255)
  description String   @db.Text
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")

  files UserFiles[]

  @@map("files_types")
}

model UserFiles {
  id        Int      @id @default(autoincrement())
  typeId    Int      @map("type_id")
  userId    Int      @map("user_id")
  path      String   @db.VarChar(255)
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  fileType FileTypes? @relation(fields: [typeId], references: [id])
  user     Users      @relation(fields: [userId], references: [id])

  @@map("user_files")
}

model UserBenefits {
  id        Int @id @default(autoincrement())
  benefitId Int @map("benefit_id")
  userId    Int @map("user_id")

  benefits Benefits @relation(fields: [benefitId], references: [id])
  user     Users    @relation(fields: [userId], references: [id])

  @@unique([userId, benefitId])
  @@map("user_benefits")
}

model Cities {
  code         String @id @db.Char(7)
  name         String @db.VarChar(255)
  state        String @db.VarChar(255)
  stateAcronym String @map("state_acronym") @db.Char(2)

  address Addresses[]

  @@map("cities")
}

model Addresses {
  id           Int       @id @default(autoincrement())
  cityCode     String    @map("city_code") @db.Char(7)
  street       String    @db.VarChar(255)
  number       String    @db.VarChar(7)
  neighborhood String    @db.Text
  zipCode      String    @map("zip_code") @db.VarChar(255)
  complement   String?   @db.Text
  createdAt    DateTime  @default(now()) @map("created_at")
  updatedAt    DateTime  @updatedAt @map("updated_at")
  deletedAt    DateTime? @map("deleted_at")

  userAddress UserAddresses[]

  city Cities @relation(fields: [cityCode], references: [code])

  @@map("addresses")
}

model UserAddresses {
  id            Int       @id @default(autoincrement())
  userId        Int       @map("user_id")
  addressId     Int       @map("address_id")
  homeInsurance Boolean   @default(false) @map("home_insurance")
  deletedAt     DateTime? @map("deleted_at")

  address Addresses? @relation(fields: [addressId], references: [id])
  user    Users      @relation(fields: [userId], references: [id])

  @@map("user_addresses")
}
