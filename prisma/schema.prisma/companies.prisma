model Companies {
  id            Int      @id @default(autoincrement())
  cnpj          String   @unique @db.<PERSON><PERSON>(14)
  corporateName String?  @map("corporate_name") @db.VarChar(255)
  fantasyName   String   @map("fantasy_name") @db.Var<PERSON>har(255)
  createdAt     DateTime @default(now()) @map("created_at")
  updatedAt     DateTime @updatedAt @map("updated_at")

  supplier Suppliers?
  user     Users[]

  @@map("companies")
}
