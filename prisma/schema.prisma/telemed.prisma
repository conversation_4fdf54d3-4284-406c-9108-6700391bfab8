enum Status {
  SCHEDULED
  CANCELED
  CANCELLED
  PENDING
  COMPLETED
}

model TelemedUsers {
  id            Int       @id @default(autoincrement())
  supplierId    Int       @map("supplier_id")
  userId        Int       @unique @map("user_id")
  userTelemedId String    @map("user_telemed_id") @db.VarChar(255)
  registeredAt  DateTime  @default(now()) @map("registered_at")
  deletedAt     DateTime? @map("deleted_at")

  supplier Suppliers @relation(fields: [supplierId], references: [id])
  user     Users     @relation(fields: [userId], references: [id])

  telemedSettings     TelemedSettings[]
  telemedAppointments TelemedAppointments[]

  @@unique([userId, userTelemedId])
  @@map("telemed_users")
}

// Configurações de usuário
model TelemedSettings {
  id            Int      @id @default(autoincrement())
  telemedUserId Int      @map("telemed_user_id")
  name          String   @db.VarChar(255)
  description   String   @db.Text
  value         Json     @db.JsonB
  createdAt     DateTime @default(now()) @map("created_at")
  updatedAt     DateTime @updatedAt @map("updated_at")

  telemedUser TelemedUsers @relation(fields: [telemedUserId], references: [id])

  @@map("telemed_settings")
}

// Consultas realizadas pelo usuário
model TelemedAppointments {
  id              Int       @id @default(autoincrement())
  externalId      String    @map("external_id") @db.VarChar(255)
  telemedUserId   Int       @map("telemed_user_id")
  statusName      Status    @default(SCHEDULED) @map("status_name")
  appointmentDate DateTime? @map("appointment_date")
  createdAt       DateTime  @default(now()) @map("created_at")
  updatedAt       DateTime  @updatedAt @map("updated_at")

  telemedUser TelemedUsers      @relation(fields: [telemedUserId], references: [id])
  status      AppointmentStatus @relation(fields: [statusName], references: [name])

  // Um mesmo usuário não pode ter duas consultas com o mesmo externalId,
  // mas usuários diferentes podem, se uma das consultas estiver CANCELLED
  @@unique([externalId, telemedUserId])
  @@map("telemed_appointments")
}

model AppointmentStatus {
  name        Status @unique
  description String @db.Text

  telemedAppointments TelemedAppointments[]
  televetAppointments TelevetAppointments[]

  @@map("appointment_status")
}
