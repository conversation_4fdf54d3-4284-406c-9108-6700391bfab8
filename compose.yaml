services:
  api:
    #    image: node:22-alpine
    #    working_dir: /app
    #    command: sh -c 'yarn install && yarn dev'
    build:
      context: .
      dockerfile: .docker/dev.Dockerfile
    env_file:
      - .env
    environment:
      NODE_ENV: local
      DATABASE_URL: *************************************/aliviae?schema=public
    ports:
      - '3333:3000'
    volumes:
      - .:/app
      - /app/node_modules
    depends_on:
      db:
        condition: service_healthy
    healthcheck:
      test: ['CMD', 'curl', '-f', 'http://localhost:3000/healthcheck']
      interval: 10s
      timeout: 5s
      retries: 5

  db:
    image: postgres:17-alpine
    user: postgres
    volumes:
      - db-data:/var/lib/postgresql/data
    environment:
      POSTGRES_DB: aliviae
      POSTGRES_PASSWORD: aliviae
    ports:
      - '5432:5432'
    healthcheck:
      test: ['CMD', 'pg_isready']
      interval: 10s
      timeout: 5s
      retries: 5

  db-ui:
    image: dpage/pgadmin4
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: aliviae
    ports:
      - '5050:80'
  nginx:
    image: nginx:alpine
    ports:
      - '8080:80'
    volumes:
      - ./nginx/default.conf:/etc/nginx/conf.d/default.conf
    depends_on:
      - api

volumes:
  db-data:
