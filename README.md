<h1 align="center"><PERSON><PERSON><PERSON>end</h1><p align="center">O Aliviae Backend é uma API RESTful, que fornece funcionalidades para consultas online, telemedicina, veterinária e descontos.</p>

### Pré-requisitos
<PERSON>çar, você vai precisar ter instalado em sua máquina as seguintes ferramentas:
[Docker](https://docs.docker.com/engine/install/ubuntu/#install-using-the-convenience-script)

<PERSON><PERSON><PERSON> disso, se você estiver em um ambiente Windows, utilize Windows Subsystem for Linux [WSL](https://learn.microsoft.com/pt-br/windows/wsl/install)

Instale o WSL e o Docker:

```bash
# Abra o PowerShell como administrador e execute o comando:
$ wsl --install

# Abra o terminal do WSL e execute os comandos:
$ curl -fsSL https://get.docker.com -o get-docker.sh
$ sudo sh ./get-docker.sh
```

### :game_die: <PERSON><PERSON>o o projeto (servidor)
```bash
# Clone este repositório no wsl
$ <NAME_EMAIL>:J17Bank/Aliviae-Backend.git

# Crie um arquivo .env na raiz do projeto
$ touch .env

# Configure as variáveis de ambiente do seu .env
# Solicite as credenciais necessários para o responsável

# Instale as dependências do projeto
$ yarn install

# Configure o prisma
$ yarn prisma migrate dev

# Rode o seeds no banco
$ yarn prisma db seed

# Inicie docker com:
$ docker compose up -d

# Acesse o container:
$ docker compose exec app bash

```

### TESTES
```bash
# Execute o comando para executar os testes
$ yarn test
```

:hammer_and_wrench: Tecnologias utilizadas

As seguintes ferramentas foram usadas na construção do projeto:

* NestJS
* TypeScript
* Prisma
* PostgreSQL
* Docker
* Jest