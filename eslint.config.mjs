// @ts-check

import eslint from '@eslint/js';
import globals from 'globals';

import tseslint from 'typescript-eslint';
import prettier from 'eslint-plugin-prettier/recommended';
import jest from 'eslint-plugin-jest';

const eslintConfig = tseslint.config(
  {
    ignores: [
      'eslint.config.mjs',
      'dist/',
      'node_modules/',
      '.git/',
      'prisma/',
      'src/metadata.ts',
    ],
  },
  eslint.configs.recommended,
  ...tseslint.configs.recommended,

  {
    languageOptions: {
      globals: globals.node,
    },
    rules: {
      '@typescript-eslint/interface-name-prefix': 'off',
      '@typescript-eslint/explicit-function-return-type': 'off',
      '@typescript-eslint/explicit-module-boundary-types': 'off',
      '@typescript-eslint/no-explicit-any': 'off',
    },
  },
  {
    files: ['**/*.spec.ts'],
    ...jest.configs['flat/recommended'],
    ...jest.configs['flat/style'],
  },
  {
    files: ['**/*.{ts,tsx}'],
    ...prettier,
  }
);

export default eslintConfig;
