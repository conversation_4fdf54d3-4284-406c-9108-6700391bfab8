# syntax=docker/dockerfile:1

ARG NODE_VERSION=22

# Use node image for base image for all stages.
FROM node:${NODE_VERSION}-alpine AS base

# Set working directory for all build stages.
WORKDIR /app

# Installs OpenSSL for Prisma compatibility
RUN apk update; \
    apk add --no-cache \
    openssl

COPY package.json yarn.lock .yarnclean ./
COPY prisma ./prisma/

# Download additional development dependencies before building, as some projects require
# "devDependencies" to be installed to build. If you don't need this, remove this step.
RUN yarn install --frozen-lockfile

# Generate prisma types and build the application.
RUN yarn prisma generate --schema prisma/schema

CMD [ "yarn", "dev" ]
