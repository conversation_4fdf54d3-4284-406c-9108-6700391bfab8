# syntax=docker/dockerfile:1

ARG NODE_VERSION=22

# Use node image for base image for all stages.
FROM node:${NODE_VERSION}-alpine AS base

# Set working directory for all build stages.
WORKDIR /usr/src/app

# Create a stage for installing production dependecies.
FROM base AS deps

# Download dependencies as a separate step to take advantage of <PERSON><PERSON>'s caching.
# Leverage a cache mount to /root/.yarn to speed up subsequent builds.
# Leverage bind mounts to package.json and yarn.lock to avoid having to copy them
# into this layer.
RUN --mount=type=bind,source=package.json,target=package.json \
    --mount=type=bind,source=yarn.lock,target=yarn.lock \
    --mount=type=bind,source=.yarnclean,target=.yarnclean \
    --mount=type=cache,target=/root/.yarn \
    yarn install --production --frozen-lockfile

# Create a stage for building the application.
FROM deps AS build

# Download additional development dependencies before building, as some projects require
# "devDependencies" to be installed to build. If you don't need this, remove this step.
RUN --mount=type=bind,source=package.json,target=package.json \
    --mount=type=bind,source=yarn.lock,target=yarn.lock \
    --mount=type=bind,source=.yarnclean,target=.yarnclean \
    --mount=type=cache,target=/root/.yarn \
    yarn install --frozen-lockfile

# Copy the rest of the source files into the image.
COPY . .

# Generate prisma types and build the application.
RUN yarn prisma generate --schema prisma/schema && yarn build

# Create a new stage to run the application with minimal runtime dependencies
# where the necessary files are copied from the build stage.
FROM base AS final

# Installs OpenSSL for Prisma compatibility
RUN apk update; \
    apk add --no-cache \
    openssl

# Use production node environment by default.
ENV NODE_ENV=production

# Run the application as a non-root user.
USER node

# Copy package.json so that package manager commands can be used.
COPY package.json nest-cli.json ./

# Copy the production dependencies from the deps stage and also
# the built application from the build stage into the image.
COPY --chown=node:node --from=deps /usr/src/app/node_modules ./node_modules
COPY --chown=node:node --from=build /usr/src/app/node_modules/@prisma ./node_modules/@prisma
COPY --chown=node:node --from=build /usr/src/app/node_modules/.prisma ./node_modules/.prisma
COPY --chown=node:node --from=build /usr/src/app/dist ./dist
COPY --chown=node:node --from=build /usr/src/app/src/resources ./src/resources
COPY --chown=node:node --from=build /usr/src/app/prisma ./prisma

CMD [ "yarn", "start:prod" ]
